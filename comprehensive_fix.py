#!/usr/bin/env python3
"""
Comprehensive script to fix all category and bracket issues.
"""

import os
import re
import glob

def fix_file(filepath):
    """Fix all issues in a single markdown file."""
    print(f"Fixing: {filepath}")
    
    with open(filepath, 'r', encoding='utf-8') as f:
        content = f.read()
    
    original_content = content
    
    # Fix multiple closing brackets
    content = re.sub(r'\]\]+', ']', content)
    
    # Fix any remaining bracket issues in tags and categories
    content = re.sub(r'tags: \[([^\]]+)\]\]', r'tags: [\1]', content)
    content = re.sub(r'categories: \[([^\]]+)\]\]', r'categories: [\1]', content)
    
    if content != original_content:
        with open(filepath, 'w', encoding='utf-8') as f:
            f.write(content)
        print(f"  ✓ Fixed issues")
        return True
    else:
        print(f"  - No issues found")
        return False

def main():
    """Main function."""
    technology_dir = "content/posts/technology"
    
    # Find all markdown files
    md_files = glob.glob(os.path.join(technology_dir, "*.md"))
    
    fixed_count = 0
    for filepath in sorted(md_files):
        if fix_file(filepath):
            fixed_count += 1
    
    print(f"Fixed {fixed_count} files.")

if __name__ == "__main__":
    main()
