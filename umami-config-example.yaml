# Umami Analytics 配置示例
# 将以下配置添加到您的 hugo.yaml 文件中

params:
  analytics:
    # Umami Analytics 配置
    umami:
      enable: true
      # Umami 服务器地址
      server: "https://umami.wenhaofree.com"
      # 网站 ID (从 Umami 仪表板获取)
      websiteId: "871f4046-f21c-4e43-aae2-483143b6fb04"
      # 是否遵循浏览器的 "Do Not Track" 设置
      respectDoNotTrack: false
      # 可选配置
      domains: ""        # 多域名跟踪，逗号分隔
      autoTrack: "true"  # 自动跟踪页面浏览
      doNotTrack: "false" # 启用请勿跟踪
      cache: "false"     # 启用缓存

# 完整的 Analytics 配置示例
params:
  analytics:
    # Google Analytics 4
    google:
      id: "G-XXXXXXXXXX"
      respectDoNotTrack: false
    
    # Umami Analytics
    umami:
      enable: true
      server: "https://umami.wenhaofree.com"
      websiteId: "871f4046-f21c-4e43-aae2-483143b6fb04"
      respectDoNotTrack: false
      autoTrack: "true"
      doNotTrack: "false"
      cache: "false"
    
    # Google AdSense
    adsense:
      enable: true
      client: "ca-pub-1234567890123456"
      defaultSlot: "1234567890"
      autoAds: true
      topAd: true
      bottomAd: true

# Umami 特性说明:
# 1. 轻量级: 脚本大小仅约 2KB
# 2. 隐私友好: 不使用 cookies，符合 GDPR
# 3. 自托管: 完全控制您的数据
# 4. 实时统计: 实时查看访问者数据
# 5. 开源: 完全开源，可自定义

# 高级配置选项:
# - domains: "example.com,blog.example.com" (多域名跟踪)
# - autoTrack: "false" (禁用自动页面跟踪)
# - doNotTrack: "true" (启用请勿跟踪)
# - cache: "true" (启用脚本缓存)

# 注意事项:
# 1. websiteId 必须从您的 Umami 仪表板获取
# 2. server 地址必须指向您的 Umami 实例
# 3. 确保 Umami 服务器可以正常访问
# 4. 在开发环境中也会正常跟踪（与 Google Analytics 不同）
