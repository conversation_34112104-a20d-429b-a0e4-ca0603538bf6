---
slug: How-to-Build-a-Knowledge-Base-with-Notion---Know-How
title: 如何利用Notion建立知识库 - 知乎
date: 2023-09-07
authors: wenhao
tags: ['Notion']
keywords: ['Default']
---
https://zhuanlan.zhihu.com/p/143274641 

之前我在 
中提到，”我不仅用Notion来做规划，它还是我写文章，收藏网站，进行项目管理的地方。“这篇文章，就重点讲一下我是如何用Notion存储和管理我的资源的。 
话不多说，直接上图： 
![Image](https://prod-files-secure.s3.us-west-2.amazonaws.com/b0012720-ccd1-41ef-9ca9-02f55a45f30f/8abc82cb-82fb-4dfb-980b-5631793ffb35/v2-edf2345e1dcdea5837d6d0f952797bc8_720w.webp?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Content-Sha256=UNSIGNED-PAYLOAD&X-Amz-Credential=AKIAT73L2G45HZZMZUHI%2F20231125%2Fus-west-2%2Fs3%2Faws4_request&X-Amz-Date=20231125T072120Z&X-Amz-Expires=3600&X-Amz-Signature=4836b521c739032249b675b8473eb4add6aeafc9b38272a42394e17b1cb633bd&X-Amz-SignedHeaders=host&x-id=GetObject)
Work Flow： 
![Image](https://prod-files-secure.s3.us-west-2.amazonaws.com/b0012720-ccd1-41ef-9ca9-02f55a45f30f/129d0d3b-2a63-4d45-8ea8-2ab600eff6a7/v2-9b2e3454096009c1419cd556250ae25d_720w.webp?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Content-Sha256=UNSIGNED-PAYLOAD&X-Amz-Credential=AKIAT73L2G45HZZMZUHI%2F20231125%2Fus-west-2%2Fs3%2Faws4_request&X-Amz-Date=20231125T072120Z&X-Amz-Expires=3600&X-Amz-Signature=76558f09630e371e63217473e30217ad605058b452497a6862ef0bf676a5e20f&X-Amz-SignedHeaders=host&x-id=GetObject)
## 第一步 剪裁保存

<!-- truncate -->

在阅读到我觉得有价值的内容时（包括文章、视频），如果是在Mac上，我会用浏览器插件保存至我的Notion中，同时在保存时添加自己一两句话的评论。 
![Image](https://prod-files-secure.s3.us-west-2.amazonaws.com/b0012720-ccd1-41ef-9ca9-02f55a45f30f/49a2a994-c68f-4ea8-9fe7-347e12501f7e/v2-a35eb76806e324d869ca28c91d29e033_720w.webp?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Content-Sha256=UNSIGNED-PAYLOAD&X-Amz-Credential=AKIAT73L2G45HZZMZUHI%2F20231125%2Fus-west-2%2Fs3%2Faws4_request&X-Amz-Date=20231125T072120Z&X-Amz-Expires=3600&X-Amz-Signature=1356fb1c3b807ba023048c3df1e8bae07f187103282578f3ea34be467ed95b04&X-Amz-SignedHeaders=host&x-id=GetObject)
点击Save Page后，文章就会自动出现在我的Notion Inbox中， 
如果是在手机端，我会用分享功能分享到我的Notion中，效果与浏览器一样： 
## 第二步 增加标签

<!-- truncate -->

保存进Notion后，第二步要做的就是找时间在Notion里为最近已收藏的内容增加标签 。 
在使用标签之前，我是使用 目录树 的层级方式管理我的知识库，但是随着我的收藏内容增加，目录树的弊端很快暴露。 
第一，每篇收藏我都必须考虑应该放在哪个目录的哪个子目录下，如果没有该目录，我还需要新建目录去管理它，这个过程效率较低。 
第二，有的收藏是属于多个目录，这时候用层级的方式去管理其实是不合适的。 
第三，目录树的整理方式，不便于查找。回想一下我们想找东西，我们最先想到的可能是一些关键词，而不是它属于哪个分类，哪个子分类。 
所以经过一段时间改进后，我采用的方式是 标签管理 。 这是我认为收藏和查找效率都更快捷、灵活的方式。 
在这里建议使用的Notion Block是Database ，它能我们管理方便内容浩繁的知识库。例如我想要看产品相关的内容，只需在视图上方点击Filter，选中「产品」标签，我就可以获得「产品」相关收藏的视图。 
打开任何一条可以进入详情视图，里面有我写的评论 
## 第三步

<!-- truncate -->

输入是为了更好地输出。我看文章或视频时，经常会产生各种产品/写作的灵感，这时我会用Notion的Database记录。 
同时，我会用 Relation 将这些灵感与 知识库里的收藏 相关联，这样 输入与输出 就实现了联通。 
Relation里的内容，对应的就是我Inbox里的收藏。 
我们也可以转换成「看板」视图 
根据「发布时间」转换为「日历视图」，查看我的发布计划： 
以上就是我用Notion建立我的知识库并与输出相关联的方式。其中最关键的就是 标签管理 和 输入输出关联， 希望对大家有所帮助。 
———————————————— 
更多产品经理成长日记，时间管理心得，外文博客翻译，请关注我的公众号「原住民的自修室」 
![Image](https://prod-files-secure.s3.us-west-2.amazonaws.com/b0012720-ccd1-41ef-9ca9-02f55a45f30f/ab3a7e04-ce67-4e3b-a04e-dd82b4bfe8f4/image?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Content-Sha256=UNSIGNED-PAYLOAD&X-Amz-Credential=AKIAT73L2G45HZZMZUHI%2F20231125%2Fus-west-2%2Fs3%2Faws4_request&X-Amz-Date=20231125T072120Z&X-Amz-Expires=3600&X-Amz-Signature=521cf4a52091d6fcd334991786935c9b176e41ee56062ac71d9b513d5d0c5fd6&X-Amz-SignedHeaders=host&x-id=GetObject)



 > 在遵循创作的康庄大道上，若我的文字不慎踏入了他人的花园，请告之我，我将以最快的速度，携带着诚意和尊重，将它们从您的视野中撤去。
