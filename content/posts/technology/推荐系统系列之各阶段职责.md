---
slug: Referral-system-02
title: 推荐系统系列之各阶段职责
date: 2023-08-02
authors: wenhao
tags: [Referral]
keywords: [召回,推荐,推荐系统,<PERSON><PERSON><PERSON>,文浩Marvin]
# image: https://img.wenhaofree.com/blog/similarity_calc1.png
description: 推荐系统系列之各阶段职责
---

推荐系统是一种通过分析用户行为、偏好、兴趣和需求等信息，为用户提供个性化推荐内容的技术。推荐系统可分为以下几个主要阶段：召回、合并、过滤和排序。下面是对这些阶段作用职责的详细说明：
![图片](https://img.wenhaofree.com/blog/similarity_calc1.png)
<!-- truncate -->
1. 召回阶段（Recall）：
召回阶段的主要目标是从海量的候选物品中，检索与用户潜在需求相关的物品子集。在这个阶段，推荐系统通过各种召回策略（如协同过滤、基于内容的推荐、热门推荐等）从海量数据中快速找到与目标用户有关联的物品，这样可以在后续阶段进行更为精细化的推荐排名。

2. 合并阶段（Combination）：
经过召回阶段后，系统会得到多个来源的推荐物品。合并阶段主要负责对这些来源的物品进行整合，去除重复推荐物品，形成一个统一的候选物品池。此阶段同时也可对物品进行简单的预处理，例如物品间的相似度计算、特征提取等。

3. 过滤阶段（Filtering）：
过滤阶段主要侧重于从候选物品池中筛选出用户可能感兴趣的物品。此环节会介入一些规则或策略，例如对用户近期收藏或喜欢太多次的物品进行降权，或是根据用户设定的一些标签或规则来筛选物品。此外，过滤阶段也可以包括协同过滤、基于内容的过滤等算法，用以进一步优化和细分用户的兴趣偏好。

4. 排序阶段（Ranking）：
排序阶段对过滤后的候选物品进行精确排序。通过对用户画像、物品特征和用户-物品交互行为等信息的深入挖掘，采用机器学习或深度学习的方法进行评分预测，将得到的物品按照预测评分或者相关性排序。最终，推荐系统会将排序后的物品推送给用户，形成最终的个性化推荐结果。

总之，推荐系统的各个阶段由粗到精地挖掘和匹配用户的需求，逐步提炼和优化推荐内容，以实现个性化推荐的目的。通过有效的召回、合并、过滤和排序策略，推荐系统为用户实现了更加高效、精准和个性化的内容服务。