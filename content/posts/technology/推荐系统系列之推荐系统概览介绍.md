---
slug: Referral-system-01
title: 推荐系统系列之推荐系统概览介绍
date: 2023-08-01
authors: wenhao
tags: [Referral]
keywords: [推荐,推荐系统,<PERSON><PERSON><PERSON>,文浩,Marvin]
description: 推荐系统系列之推荐系统概览介绍
# image: https://img.wenhaofree.com/blog/similarity_calc1.png
---

## 1.1 推荐系统简介

### 学习目标

- 了解推荐系统概念及产生背景
- 记忆推荐系统工作原理及作用
- 了解推荐系统与web项目区别
<!-- truncate -->
### 1 推荐系统概念及产生背景

个性化推荐(推荐系统)经历了多年的发展，已经成为互联网产品的标配，也是AI成功落地的分支之一，在电商(淘宝/京东)、资讯(今日头条/微博)、音乐(网易云音乐/QQ音乐)、短视频(抖音/快手)等热门应用中,推荐系统都是核心组件之一。

- 什么是推荐系统

  没有明确需求的用户访问了我们的服务, 且服务的物品对用户构成了信息过载, 系统通过一定的规则对物品进行排序,并将排在前面的物品展示给用户,这样的系统就是推荐系统

- 信息过载 & 用户需求不明确

  - 分类⽬录（1990s）：覆盖少量热门⽹站。典型应用：Hao123 Yahoo
  - 搜索引擎（2000s）：通过搜索词明确需求。典型应用：Google Baidu
  - 推荐系统（2010s）：不需要⽤户提供明确的需求，通过分析⽤ 户的历史⾏为给⽤户的兴趣进⾏建模，从⽽主动给⽤户推荐能 够满⾜他们兴趣和需求的信息。

- 推荐系统 V.S. 搜索引擎

  |          | 搜索     | 推荐     |
  | -------- | -------- | -------- |
  | 行为方式 | 主动     | 被动     |
  | 意图     | 明确     | 模糊     |
  | 个性化   | 弱       | 强       |
  | 流量分布 | 马太效应 | 长尾效应 |
  | 目标     | 快速满足 | 持续服务 |
  | 评估指标 | 简明     | 复杂     |

### 2 推荐系统的工作原理及作用

- 推荐系统的工作原理

  - **社会化推荐** 向朋友咨询, 社会化推荐, 让好友给自己推荐物品
  - **基于内容的推荐** 打开搜索引擎, 输入自己喜欢的演员的名字, 然后看看返回结果中还有什么电影是自己没看过的
  - **基于流行度的推荐** 查看票房排行榜,
  - **基于协同过滤的推荐** 找到和自己历史兴趣相似的用户, 看看他们最近在看什么电影

- 推荐系统的作用

  - 高效连接用户和物品
  - 提高用户停留时间和用户活跃程度
  - 有效的帮助产品实现其商业价值

- 推荐系统的应用场景

  ![img](https://img.wenhaofree.com/blog/recommend1.png)

### 3 推荐系统和Web项目的区别

- 通过信息过滤实现目标提升 V.S. 稳定的信息流通系统
  - web项目: 处理复杂业务逻辑，处理高并发，为用户构建一个稳定的信息流通服务
  - 推荐系统: 追求指标增长, 留存率/阅读时间/GMV (Gross Merchandise Volume电商网站成交金额)/视频网站VV (Video View)
- 确定 V.S. 不确定思维
  - web项目: 对结果有确定预期
  - 推荐系统: 结果是概率问题

### 4 总结

- 推荐系统的概念及产生背景
  - 推荐系统是一个信息过滤系统
  - 解决了**信息过载**和**用户需求不明确**的问题
  - 与搜索的区别主要体现在个性化，流量分布，评价方式上
- 推荐系统的原理及作用
  - 社会化推荐 基于流行度的推荐 基于内容推荐 协同过滤推荐
  - 可以提高用户使用时长，促进主要商业指标增长
- 推荐系统与web项目的区别
  - 信息过滤系统和信息流通系统
  - 推荐系统结果是不确定的







## 1.2 推荐系统架构设计

### 学习目标

- 了解推荐系统要素
- 记忆推荐系统架构

### 1 推荐系统要素

- UI 和 UE(前端界面)
- 数据 (Lambda架构)
- 业务知识
- 算法

### 2 推荐系统架构

- 推荐系统整体架构

  ![img](https://img.wenhaofree.com/blog/%E6%8E%A8%E8%8D%90%E6%B5%81%E7%A8%8B.png)

- 大数据Lambda架构

  - Lambda架构是由实时大数据处理框架Storm的作者Nathan Marz提出的一个实时大数据处理框架。

  - Lambda架构的将离线计算和实时计算整合，设计出一个能满足实时大数据系统关键特性的架构，包括有：高容错、低延时和可扩展等。

  - 分层架构

    - 批处理层
      - 数据不可变, 可进行任何计算, 可水平扩展
      - 高延迟 几分钟~几小时(计算量和数据量不同)
      - 日志收集： Flume
      - 分布式存储： Hadoop （HDFS）
      - 分布式计算： Hadoop （MapReduce）、Spark
      - 视图存储数据库
        - nosql(HBase)
        - Redis/memcache
        - MySQL
    - 实时处理层
      - 流式处理, 持续计算
      - 存储和分析某个窗口期内的数据（一段时间的热销排行，实时热搜等）
      - 实时数据收集 flume & kafka
      - 实时数据分析 spark streaming/storm/flink
    - 服务层
      - 支持随机读
      - 需要在非常短的时间内返回结果
      - 读取批处理层和实时处理层结果并对其归并

  - Lambda架构图

    ![img](https://img.wenhaofree.com/blog/lambda3.png)

- 推荐算法架构

  - 召回阶段 (海选)
    - 召回决定了最终推荐结果的天花板
    - 常用算法:
      - **协同过滤**
      - **基于内容**
  - 排序阶段 （精选）
    - 召回决定了最终推荐结果的天花板, 排序逼近这个极限, 决定了最终的推荐效果
    - CTR预估 (点击率预估 使用LR算法) 估计用户是否会点击某个商品 需要用户的点击数据
  - 策略调整

![img](https://img.wenhaofree.com/blog/recommend7.jpeg)

### 3 总结

- 推荐系统要素
  - 界面 **数据** 业务 **算法**
- 推荐系统框架
  - 技术架构 lambda
    - 离线计算 hadoop spark
    - 实时计算 spark streaming , storm
    - 消息中间件 flume kafka
  - 业务架构
    - 召回
    - 排序
    - 策略调整







## 1.3 推荐算法

### 学习目标

- 了解推荐模型构建流程
- 理解协同过滤原理
- 记忆相似度计算方法
- 应用杰卡德相似度，皮尔逊相关系数，实现简单协同过滤推荐案例

### 1 推荐模型构建流程

Data(数据)->Features(特征)->ML Algorithm(选择算法训练模型)->Prediction Output(预测输出)

- 数据清洗/数据处理

  - 数据来源
    - 显性数据
      - Rating 打分
      - Comments 评论/评价
    - 隐形数据
      -  Order history 历史订单
      -  Cart events 加购物车
      -  Page views 页面浏览
      -  Click-thru 点击
      -  Search log 搜索记录
  - 数据量/数据能否满足要求

- 特征工程

  - 从数据中筛选特征

    - 一个给定的商品，可能被拥有类似品味或需求的用户购买

    - 使用用户行为数据描述商品

      ![img](https://img.wenhaofree.com/blog/algorithm3.png)

  - 用数据表示特征

    - 将所有用户行为合并在一起 ，形成一个user-item 矩阵

      ![1545452707102](https://img.wenhaofree.com/blog/algorithm4.png)

- 选择合适的算法，训练算法模型
  - **协同过滤**
  - 基于内容
- 产生推荐结果
  - 对推荐结果进行评估（评估方法后面章节介绍），评估通过后上线

### 2 最经典的推荐算法：协同过滤推荐算法（Collaborative Filtering）

算法思想：**物以类聚，人以群分**

基本的协同过滤推荐算法基于以下假设：

- “跟你喜好**相似的人**喜欢的东西你也很有可能喜欢” ：基于用户的协同过滤推荐（User-based CF）
- “跟你喜欢的东西**相似的东西**你也很有可能喜欢 ”：基于物品的协同过滤推荐（Item-based CF）

实现协同过滤推荐有以下几个步骤：

1. **找出最相似的人或物品：TOP-N相似的人或物品**

   通过计算两两的相似度来进行排序，即可找出TOP-N相似的人或物品

2. **根据相似的人或物品产生推荐结果**

   利用TOP-N结果生成初始推荐结果，然后过滤掉用户已经有过记录的物品或明确表示不感兴趣的物品

以下是一个简单的示例，数据集相当于一个用户对物品的购买记录表：打勾表示用户对物品的有购买记录

- 关于相似度计算先用简单方法计算：

  - 有两个同学X和Y

- X同学爱好[

  足球、篮球

  、乒乓球]

  - Y同学爱好[网球、**足球、篮球**、羽毛球]，

- 共同爱好有2个，相似度可以用：2/3 * 2/4 = 1/3 ≈ 0.33 来表示。

User-Based CF

![img](https://img.wenhaofree.com/blog/%E5%9F%BA%E4%BA%8E%E7%94%A8%E6%88%B7%E7%9A%84%E5%8D%8F%E5%90%8C%E8%BF%87%E6%BB%A4%E6%8E%A8%E8%8D%901.png)

Item-Based CF

![img](https://img.wenhaofree.com/blog/%E5%9F%BA%E4%BA%8E%E7%89%A9%E5%93%81%E7%9A%84%E5%8D%8F%E5%90%8C%E8%BF%87%E6%BB%A4%E6%8E%A8%E8%8D%901.png)

### 3 相似度计算(Similarity Calculation)

![img](https://img.wenhaofree.com/blog/similarity_calc1.png)

- 相似度的计算方法

  - 欧氏距离, 是一个欧式空间下度量距离的方法. 两个物体, 都在同一个空间下表示为两个点, 假如叫做p,q, 分别都是n个坐标, 那么欧式距离就是衡量这两个点之间的距离. 欧氏距离不适用于布尔向量之间

  ![1546159024305](https://img.wenhaofree.com/blog/od.png)

   欧氏距离的值是一个非负数, 最大值正无穷, 通常计算相似度的结果希望是[-1,1]或[0,1]之间,一般可以使用

   如下转化公式:![img](https://img.wenhaofree.com/blog/od2.png)

  - 余弦相似度

    - 度量的是两个向量之间的夹角, 用夹角的余弦值来度量相似的情况
    - 两个向量的夹角为0是,余弦值为1, 当夹角为90度是余弦值为0,为180度是余弦值为-1
    - 余弦相似度在度量文本相似度, 用户相似度 物品相似度的时候较为常用
    - 余弦相似度的特点, 与向量长度无关,余弦相似度计算要对向量长度归一化, 两个向量只要方向一致,无论程度强弱, 都可以视为'相似'

    ![img](https://img.wenhaofree.com/blog/cosine.png)

    ![img](https://img.wenhaofree.com/blog/similarity_calc5.png)

- 皮尔逊相关系数Pearson

  - 实际上也是余弦相似度, 不过先对向量做了中心化, 向量a b各自减去向量的均值后, 再计算余弦相似度
  - 皮尔逊相似度计算结果在-1,1之间 -1表示负相关, 1表示正相关
  - 度量两个变量是不是同增同减
  - 皮尔逊相关系数度量的是两个变量的变化趋势是否一致, **不适合计算布尔值向量之间的相关度**

  ![img](https://img.wenhaofree.com/blog/pearson.png)

- 杰卡德相似度 Jaccard

  - 两个集合的交集元素个数在并集中所占的比例, 非常适用于布尔向量表示
  - 分子是两个布尔向量做点积计算, 得到的就是交集元素的个数
  - 分母是两个布尔向量做或运算, 再求元素和

  ![img](https://img.wenhaofree.com/blog/jaccard.png)

- 如何选择相似度计算方法：

  - 余弦相似度/皮尔逊相关系数适合用户评分数据(实数值),
  - 杰卡德相似度适用于隐式反馈数据(0,1布尔值 是否收藏,是否点击,是否加购物车)

### 4 协同过滤推荐算法代码实现：

- 构建数据集：

  ```python
  users = ["User1", "User2", "User3", "User4", "User5"]
  items = ["Item A", "Item B", "Item C", "Item D", "Item E"]
  # 构建数据集
  datasets = [
      ["buy",None,"buy","buy",None],
      ["buy",None,None,"buy","buy"],
      ["buy",None,"buy",None,None],
      [None,"buy",None,"buy","buy"],
      ["buy","buy","buy",None,"buy"],
  ]
  ```

- 用1、0分别来表示用户的是否购买过该物品，数据集如下所示：

  ```python
  users = ["User1", "User2", "User3", "User4", "User5"]
  items = ["Item A", "Item B", "Item C", "Item D", "Item E"]
  # 用户购买记录数据集
  datasets = [
      [1,0,1,1,0],
      [1,0,0,1,1],
      [1,0,1,0,0],
      [0,1,0,1,1],
      [1,1,1,0,1],
  ]
  import pandas as pd
  
  df = pd.DataFrame(datasets,
                    columns=items,
                    index=users)
  print(df)
  ```

- 接下来进行相似度的计算，由于是0,1这样的布尔值，所以使用杰卡德相似度：

  ```python
  from sklearn.metrics import jaccard_similarity_score
  # 直接计算某两项的杰卡德相似系数
  # 计算Item A 和Item B的相似度
  print(jaccard_similarity_score(df["Item A"], df["Item B"]))
  
  # 计算所有的数据两两的杰卡德相似系数
  from sklearn.metrics.pairwise import pairwise_distances
  # 计算用户间相似度
  user_similar = 1 - pairwise_distances(df, metric="jaccard")
  user_similar = pd.DataFrame(user_similar, columns=users, index=users)
  print("用户之间的两两相似度：")
  print(user_similar)
  
  # 计算物品间相似度
  item_similar = 1 - pairwise_distances(df.T, metric="jaccard")
  item_similar = pd.DataFrame(item_similar, columns=items, index=items)
  print("物品之间的两两相似度：")
  print(item_similar)
  ```

  有了两两的相似度，接下来就可以筛选TOP-N相似结果，并进行推荐了

- User-Based CF

  ```python
  import pandas as pd
  import numpy as np
  from pprint import pprint
  
  users = ["User1", "User2", "User3", "User4", "User5"]
  items = ["Item A", "Item B", "Item C", "Item D", "Item E"]
  # 用户购买记录数据集
  datasets = [
      [1,0,1,1,0],
      [1,0,0,1,1],
      [1,0,1,0,0],
      [0,1,0,1,1],
      [1,1,1,0,1],
  ]
  
  df = pd.DataFrame(datasets,
                    columns=items,
                    index=users)
  
  # 计算所有的数据两两的杰卡德相似系数
  from sklearn.metrics.pairwise import pairwise_distances
  # 计算用户间相似度  1-杰卡德距离=杰卡德相似度
  user_similar = 1 - pairwise_distances(df, metric="jaccard")
  user_similar = pd.DataFrame(user_similar, columns=users, index=users)
  print("用户之间的两两相似度：")
  print(user_similar)
  
  topN_users = {}
  # 遍历每一行数据
  for i in user_similar.index:
      # 取出每一列数据，并删除自身，然后排序数据
      _df = user_similar.loc[i].drop([i])
      #sort_values 排序 按照相似度降序排列
      _df_sorted = _df.sort_values(ascending=False)
      # 从排序之后的结果中切片 取出前两条（相似度最高的两个）
      top2 = list(_df_sorted.index[:2])
      topN_users[i] = top2
  
  print("Top2相似用户：")
  pprint(topN_users)
  
  # 准备空白dict用来保存推荐结果
  rs_results = {}
  #遍历所有的最相似用户
  for user, sim_users in topN_users.items():
      rs_result = set()    # 存储推荐结果
      for sim_user in sim_users:
          # 构建初始的推荐结果
          rs_result = rs_result.union(set(df.loc[sim_user].replace(0,np.nan).dropna().index))
      # 过滤掉已经购买过的物品
      rs_result -= set(df.loc[user].replace(0,np.nan).dropna().index)
      rs_results[user] = rs_result
  print("最终推荐结果：")
  pprint(rs_results)
  ```

- Item-Based CF

  ```python
  import pandas as pd
  import numpy as np
  from pprint import pprint
  
  users = ["User1", "User2", "User3", "User4", "User5"]
  items = ["Item A", "Item B", "Item C", "Item D", "Item E"]
  # 用户购买记录数据集
  datasets = [
      [1,0,1,1,0],
      [1,0,0,1,1],
      [1,0,1,0,0],
      [0,1,0,1,1],
      [1,1,1,0,1],
  ]
  
  df = pd.DataFrame(datasets,
                    columns=items,
                    index=users)
  
  # 计算所有的数据两两的杰卡德相似系数
  from sklearn.metrics.pairwise import pairwise_distances
  # 计算物品间相似度
  item_similar = 1 - pairwise_distances(df.T, metric="jaccard")
  item_similar = pd.DataFrame(item_similar, columns=items, index=items)
  print("物品之间的两两相似度：")
  print(item_similar)
  
  topN_items = {}
  # 遍历每一行数据
  for i in item_similar.index:
      # 取出每一列数据，并删除自身，然后排序数据
      _df = item_similar.loc[i].drop([i])
      _df_sorted = _df.sort_values(ascending=False)
  
      top2 = list(_df_sorted.index[:2])
      topN_items[i] = top2
  
  print("Top2相似物品：")
  pprint(topN_items)
  
  rs_results = {}
  # 构建推荐结果
  for user in df.index:    # 遍历所有用户
      rs_result = set()
      for item in df.loc[user].replace(0,np.nan).dropna().index:   # 取出每个用户当前已购物品列表
          # 根据每个物品找出最相似的TOP-N物品，构建初始推荐结果
          rs_result = rs_result.union(topN_items[item])
      # 过滤掉用户已购的物品
      rs_result -= set(df.loc[user].replace(0,np.nan).dropna().index)
      # 添加到结果中
      rs_results[user] = rs_result
  
  print("最终推荐结果：")
  pprint(rs_results)
  ```

**关于协同过滤推荐算法使用的数据集**

在前面的demo中，我们只是使用用户对物品的一个购买记录，类似也可以是比如浏览点击记录、收听记录等等。这样数据我们预测的结果其实相当于是在预测用户是否对某物品感兴趣，对于喜好程度不能很好的预测。

因此在协同过滤推荐算法中其实会更多的利用用户对物品的“评分”数据来进行预测，通过评分数据集，我们可以预测用户对于他没有评分过的物品的评分。

**关于用户-物品评分矩阵**

用户-物品的评分矩阵，根据评分矩阵的稀疏程度会有不同的解决方案

- 稠密评分矩阵

  ![img](https://img.wenhaofree.com/blog/%E7%A8%A0%E5%AF%86%E8%AF%84%E5%88%86%E6%95%B0%E6%8D%AE%E9%9B%86.png)

- 稀疏评分矩阵

  ![img](https://img.wenhaofree.com/blog/%E7%A8%80%E7%96%8F%E8%AF%84%E5%88%86%E6%95%B0%E6%8D%AE%E9%9B%86.png)

这里先介绍稠密评分矩阵的处理，稀疏矩阵的处理相对会复杂一些，我们到后面再来介绍。

#### 使用协同过滤推荐算法对用户进行评分预测

- 数据集：![img](https://img.wenhaofree.com/blog/%E7%A8%A0%E5%AF%86%E8%AF%84%E5%88%86%E6%95%B0%E6%8D%AE%E9%9B%86.png)

  **目的：预测用户1对物品E的评分**

- 构建数据集：注意这里构建评分数据时，对于缺失的部分我们需要保留为None，如果设置为0那么会被当作评分值为0去对待

  ```python
  users = ["User1", "User2", "User3", "User4", "User5"]
  items = ["Item A", "Item B", "Item C", "Item D", "Item E"]
  # 用户购买记录数据集
  datasets = [
      [5,3,4,4,None],
      [3,1,2,3,3],
      [4,3,4,3,5],
      [3,3,1,5,4],
      [1,5,5,2,1],
  ]
  ```

- 计算相似度：对于评分数据这里我们采用皮尔逊相关系数[-1,1]来计算，-1表示强负相关，+1表示强正相关

  > pandas中corr方法可直接用于计算皮尔逊相关系数

  ```python
  df = pd.DataFrame(datasets,
                    columns=items,
                    index=users)
  
  print("用户之间的两两相似度：")
  # 直接计算皮尔逊相关系数
  # 默认是按列进行计算，因此如果计算用户间的相似度，当前需要进行转置
  user_similar = df.T.corr()
  print(user_similar.round(4))
  
  print("物品之间的两两相似度：")
  item_similar = df.corr()
  print(item_similar.round(4))
  ```

  ```
  # 运行结果：
  用户之间的两两相似度：
          User1   User2   User3   User4   User5
  User1  1.0000  0.8528  0.7071  0.0000 -0.7921
  User2  0.8528  1.0000  0.4677  0.4900 -0.9001
  User3  0.7071  0.4677  1.0000 -0.1612 -0.4666
  User4  0.0000  0.4900 -0.1612  1.0000 -0.6415
  User5 -0.7921 -0.9001 -0.4666 -0.6415  1.0000
  物品之间的两两相似度：
          Item A  Item B  Item C  Item D  Item E
  Item A  1.0000 -0.4767 -0.1231  0.5322  0.9695
  Item B -0.4767  1.0000  0.6455 -0.3101 -0.4781
  Item C -0.1231  0.6455  1.0000 -0.7206 -0.4276
  Item D  0.5322 -0.3101 -0.7206  1.0000  0.5817
  Item E  0.9695 -0.4781 -0.4276  0.5817  1.0000
  ```

  可以看到与用户1最相似的是用户2和用户3；与物品A最相似的物品分别是物品E和物品D。

  **注意：**我们在预测评分时，往往是通过与其有正相关的用户或物品进行预测，如果不存在正相关的情况，那么将无法做出预测。这一点尤其是在稀疏评分矩阵中尤为常见，因为稀疏评分矩阵中很难得出正相关系数。

- **评分预测：**

  **User-Based CF 评分预测：使用用户间的相似度进行预测**

  关于评分预测的方法也有比较多的方案，下面介绍一种效果比较好的方案，该方案考虑了用户本身的评分评分以及近邻用户的加权平均相似度打分来进行预测： 我们要预测用户1对物品E的评分，那么可以根据与用户1最近邻的用户2和用户3进行预测，计算如下：
  pred(u1,i5)=0.85∗3+0.71∗50.85+0.71=3.91����(�1,�5)=0.85∗3+0.71∗50.85+0.71=3.91 最终预测出用户1对物品5的评分为3.91

  **Item-Based CF 评分预测：使用物品间的相似度进行预测**

  这里利用物品相似度预测的计算同上，同样考虑了用户自身的平均打分因素，结合预测物品与相似物品的加权平均相似度打分进行来进行预测 

**User-Based CF与Item-Based CF比较**

- User-Based CF和Item-Based CF 严格意义上属于两种不同的推荐算法（预测评分结果存在差异）
- User-Based CF应用场景：用户少于物品，或者用户没用物品变化快的场景 （举例，信息流产品）
- Item-Based CF应用场景：用户多于物品，或者物品变化较慢的场景 （举例，电商应用）
- 不好确定那种更合适，可以两种算法都去实现，然后对推荐效果进行评估分析选出更优方案

### 5 总结

- 推荐模型构建流程

  - 数据处理->特征工程->训练算法模型->评估上线

- 理解协同过滤原理

  - 基本思想：人以类聚，物以群分
  - 对用户行为数据量要求较高
  - USER-ITEM 评分/消费记录 矩阵

- 相似度计算方法

  - 皮尔逊，余弦相似度 适合预测评分数据

    杰卡德相似度 适合预测是否消费/是否点击 0/1数据

- 协同过滤推荐案例

  - 皮尔逊相关系数适合稠密数据情况
  - 基于用户协同过滤和基于物品协同过滤两种协同过滤推荐算法比较





## 1.5 推荐系统评估

### 学习目标

- 了解推荐系统的常用评估指标
- 了解推荐系统的评估方法

### 1 推荐系统的评估指标

- 好的推荐系统可以实现用户, 服务提供方, 内容提供方的共赢

![img](https://img.wenhaofree.com/blog/recommend2.png)

- 评估数据来源显示反馈和隐式反馈

  |          | 显式反馈                       | 隐式反馈                 |
  | -------- | ------------------------------ | ------------------------ |
  | 例子     | 电影/书籍评分 是否喜欢这个推荐 | 播放/点击 评论 下载 购买 |
  | 准确性   | 高                             | 低                       |
  | 数量     | 少                             | 多                       |
  | 获取成本 | 高                             | 低                       |

- 常用评估指标

  • 准确性 • 信任度 • 满意度 • 实时性 • 覆盖率 • 鲁棒性 • 多样性 • 可扩展性 • 新颖性 • 商业⽬标 • 惊喜度 • ⽤户留存

  - 准确性 (理论角度)

    - 评分预测

      - RMSE （均方根误差）

        ![img](https://img.wenhaofree.com/blog/rmse.webp)

      - MAE (平均绝对误差)

        ![img](https://img.wenhaofree.com/blog/mae.webp)

    - topN推荐

      - 召回率 精准率

  - 准确性 (业务角度)

  ![img](https://img.wenhaofree.com/blog/recommend3.png)

  - 覆盖度
    - 信息熵 对于推荐越大越好
    - 覆盖率
  - 多样性&新颖性&惊喜性
    - 多样性：推荐列表中两两物品的不相似性。
    - 新颖性：未曾关注的类别、作者；
    - 惊喜性：历史不相似（惊）但很满意（喜）
    - 往往需要牺牲准确性
  - Exploitation & Exploration 探索与利用问题
    - Exploitation(开发 利用)：选择现在可能最佳的⽅案
    - Exploration(探测 搜索)：选择现在不确定的⼀些⽅案，但未来可能会有⾼收益的⽅案
    - 在做两类决策的过程中，不断更新对所有决策的不确定性的认知，优化 长期的⽬标
  - EE问题实践
    - 兴趣扩展: 相似话题, 搭配推荐
    - 人群算法: userCF 用户聚类
    - 平衡个性化推荐和热门推荐比例
    - 随机丢弃用户行为历史
    - 随机扰动模型参数
  - EE可能带来的问题
    - 探索伤害用户体验, 可能导致用户流失
    - 探索带来的长期收益(留存率)评估周期长

### 2 推荐系统评估方法

- 评估方法
  - 问卷调查: 成本高
  - 离线评估:
    - 只能在用户看到过的候选集上做评估, 且跟线上真实效果存在偏差
    - 只能评估少数指标
    - 速度快, 不损害用户体验
  - 在线评估: 灰度发布 & A/B测试
    - A/B测试 ：将不同算法同时上线，分配一定比例的流量，运行一段时间观察关键指标，可以与基于流行度推荐作对比
    - 灰度发布：按照一定比例，控制能够看到某个策略的用户数量，比如5%灰度，只有5%的用户能看到
  - 实践: 离线评估和在线评估结合, 定期做问卷调查

### 3 总结

- 推荐系统的评估指标
  - 准确性
  - 覆盖度
  - 多样性&新颖性&惊喜性
  - Exploitation & Exploration 探索与利用问题
- 推荐系统评估方法
  - 离线评估和在线评估结合，定期做问卷调查



## 1.6 推荐系统的冷启动问题

### 学习目标

- 记忆推荐系统冷启动概念
- 了解处理推荐系统冷启动的常用方法

### 1 推荐系统冷启动概念

- ⽤户冷启动：如何为新⽤户做个性化推荐
- 物品冷启动：如何将新物品推荐给⽤户
- 系统冷启动：⽤户冷启动+物品冷启动
- 冷启动问题本质：在缺少历史数据的情况下如何预测⽤户偏好

### 2 处理推荐系统冷启动问题的常用方法

- 用户冷启动：尽可能收集用户特征数据

  - 1 收集用户的基本信息

    - ⽤户注册信息：性别、年龄、地域
    - 设备信息：定位、⼿机型号、app列表

  - 2 引导用户填写兴趣

    ![img](https://img.wenhaofree.com/blog/recommend5.png)

  - 3 使用其它站点/应用的行为数据, 例如腾讯视频&QQ音乐

    ![img](https://img.wenhaofree.com/blog/recommend4.png)

  - 4 新老用户推荐策略的差异

    - 新⽤户在冷启动阶段更倾向于热门排⾏榜，⽼⽤户会偏向个性化推荐
    - Explore Exploit⼒度不同

- 物品冷启动

  - 给物品打标签
    - 系统的业务数据中产生
    - 从其它网站爬取
  - 利用物品的内容信息，将新物品先投放给曾经喜欢过和它内容相似的其他物品的用户。

  ![img](https://img.wenhaofree.com/blog/firststart2.png)

- 系统冷启动

  - 基于内容的推荐 系统早期
  - 基于内容的推荐逐渐过渡到协同过滤
  - 基于内容的推荐会一直存在，综合考虑基于内容推荐和协同过滤的结果

### 3 总结

- 推荐系统冷启动概念
  - 用户冷启动，物品冷启动，系统冷启动
  - 本质是缺少用户行为数据
- 处理推荐系统冷启动问题的常用方法
  - 尽可能收集信息
    - 收集用户信息-用户聚类
    - 提取物品信息-基于内容推荐
  - 使用基于内容推荐




## 参考文章：
- [入门推荐系统](https://www.woshipm.com/pd/4223123.html)
- [推荐系统概览](https://aws.amazon.com/cn/blogs/china/recommended-system-overview-of-recommended-system-series-part-1/)

