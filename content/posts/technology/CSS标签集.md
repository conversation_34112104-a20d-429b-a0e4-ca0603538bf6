---
slug: CSS-lable
title: CSS常用标签集合汇总
date: 2023-07-27
authors: we<PERSON><PERSON>
tags: [Web,CSS]
keywords: [Web,CSS,WenHao,文浩,<PERSON>]
description: CSS常用标签集合汇总
---


CSS（层叠样式表）是一种用于控制网页样式和布局的语言。以下是CSS中常用的标签及其解释和示例：

1. `color`：用于控制文本颜色。

   代码示例：

   `````css
   p {
     color: red;
   }
   ```
<!-- truncate -->

2. `background-color`：用于控制背景颜色。

   代码示例：

   ````css
   body {
     background-color: #f2f2f2;
   }
   ```

3. `font-size`：用于控制字体大小。

   代码示例：

   ````css
   h1 {
     font-size: 36px;
   }
   ```

4. `font-family`：用于控制字体样式。

   代码示例：

   ````css
   body {
     font-family: Arial, sans-serif;
   }
   ```

5. `text-align`：用于控制文本对齐方式。

   代码示例：

   ````css
   p {
     text-align: center;
   }
   ```

6. `border`：用于控制边框样式和宽度。

   代码示例：

   ````css
   .box {
     border: 1px solid black;
   }
   ```

7. `padding`：用于控制内边距。

   代码示例：

   ````css
   .box {
     padding: 10px;
   }
   ```

8. `margin`：用于控制外边距。

   代码示例：

   ````css
   .box {
     margin: 10px;
   }
   ```

9. `display`：用于控制元素的显示方式。

   代码示例：

   ````css
   .box {
     display: inline-block;
   }
   ```

10. `float`：用于控制元素的浮动方式。

    代码示例：

    ```css
    .box {
      float: left;
    }
    ```

11. `position`：用于控制元素的定位方式。

    代码示例：

    ```css
    .box {
      position: relative;
      top: 50px;
      left: 50px;
    }
    ```

12. `opacity`：用于控制元素的不透明度。

    代码示例：

    ```css
    .box {
      opacity: 0.5;
    }
    ```

以上是CSS中常用的标签及其解释和示例。

好的，以下是CSS中常用的标签，其中已经在之前回答中提到的标签将不再重复解释：

1. `text-decoration`：用于控制文本修饰，如下划线、删除线等。

   代码示例：

   `````css
   a {
     text-decoration: none;
   }
   
   a:hover {
     text-decoration: underline;
   }
   ```

2. `list-style`：用于控制列表样式，如圆点、数字等。

   代码示例：

   ````css
   ul {
     list-style: disc;
   }
   
   ol {
     list-style: decimal;
   }
   ```

3. `line-height`：用于控制行高。

   代码示例：

   ````css
   p {
     line-height: 1.5;
   }
   ```

4. `text-transform`：用于控制文本大小写。

   代码示例：

   ````css
   h1 {
     text-transform: uppercase;
   }
   
   p {
     text-transform: lowercase;
   }
   ```

5. `font-weight`：用于控制字体粗细。

   代码示例：

   ````css
   h1 {
     font-weight: bold;
   }
   
   p {
     font-weight: normal;
   }
   ```

6. `text-shadow`：用于添加文本阴影效果。

   代码示例：

   ````css
   h1 {
     text-shadow: 2px 2px 2px #ccc;
   }
   ```

7. `box-shadow`：用于添加盒子阴影效果。

   代码示例：

   ````css
   .box {
     box-shadow: 2px 2px 2px #ccc;
   }
   ```

8. `background-image`：用于添加背景图片。

   代码示例：

   ````css
   body {
     background-image: url("bg.jpg");
   }
   ```

9. `background-repeat`：用于控制背景图片的重复方式。

   代码示例：

   ````css
   body {
     background-repeat: no-repeat;
   }
   ```

10. `background-position`：用于控制背景图片的位置。

    代码示例：

    ```css
    body {
      background-position: center;
    }
    ```

11. `background-size`：用于控制背景图片的大小。

    代码示例：

    ```css
    body {
      background-size: cover;
    }
    ```

以上是CSS中常用的标签及其解释和示例。