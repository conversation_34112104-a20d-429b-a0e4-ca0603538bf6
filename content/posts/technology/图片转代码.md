---
slug: Image-transcoding
title: 图片转代码
date: 2023-11-20
authors: we<PERSON><PERSON>
tags: ['General']
keywords: ['Default']
---
https://github.com/abi/screenshot-to-code 

# screenshot-to-code

This simple app converts a screenshot to HTML/Tailwind CSS. It uses GPT-4 Vision to generate the code and DALL-E 3 to generate similar-looking images. 
Details 
Youtube.Clone.mp4 
See the  Examples  section below for more demos. 
## 🚀 Try It Out!

<!-- truncate -->

🆕  Try it here  (bring your own OpenAI key -  your key must have access to GPT-4 Vision. See  FAQ  section below for details ). Or see  Getting Started  below for local install instructions. 
## 🌟 Recent Updates

<!-- truncate -->

- Nov 19 - Support for dark/light code editor theme - thanks 
- https://github.com/kachbit
- Nov 16 - Added a setting to disable DALL-E image generation if you don't need that
- Nov 16 - View code directly within the app
- Nov 15 - 🔥 You can now instruct the AI to update the code as you wish. It is helpful if the AI messed up some styles or missed a section.
## 🛠 Getting Started

<!-- truncate -->

The app has a React/Vite frontend and a FastAPI backend. You will need an OpenAI API key with access to the GPT-4 Vision API. 
Run the backend (I use Poetry for package management -  pip install poetry  if you don't have it): 
```plain text
cd backend
echo "OPENAI_API_KEY=sk-your-key" > .env
poetry install
poetry shell
poetry run uvicorn main:app --reload --port 7001
```
Run the frontend: 
```plain text
cd frontend
yarn
yarn dev
```
Open  http://localhost:5173  to use the app. 
If you prefer to run the backend on a different port, update VITE_WS_BACKEND_URL in  frontend/.env.local 
## Docker

<!-- truncate -->

If you have Docker installed on your system, in the root directory, run: 
```plain text
echo "OPENAI_API_KEY=sk-your-key" > .env
docker-compose up -d --build
```
The app will be up and running at  http://localhost:5173 . Note that you can't develop the application with this setup as the file changes won't trigger a rebuild. 
## 🙋‍♂️ FAQs

<!-- truncate -->

- I'm running into an error when setting up the backend. How can I fix it?
-  
- Try this
- . If that still doesn't work, open an issue.
- How do I get an OpenAI API key that has the GPT4 Vision model available?
-  Create an OpenAI account. And then, you need to buy at least $1 worth of credit on the 
- Billing dashboard
- .
- How can I provide feedback?
-  For feedback, feature requests and bug reports, open an issue or ping me on 
- Twitter
- .
## 📚 Examples

<!-- truncate -->

NYTimes 
Instagram page (with not Taylor Swift pics) 
Details 
instagram.taylor.swift.take.1.mp4 
Hacker News  but it gets the colors wrong at first so we nudge it 
Details 
hacker.news.with.edits.mp4 
## 🌍 Hosted Version

<!-- truncate -->

🆕  Try it here  (bring your own OpenAI key -  your key must have access to GPT-4 Vision. See  FAQ  section for details ). Or see  Getting Started  for local install instructions. 



 > 在遵循创作的康庄大道上，若我的文字不慎踏入了他人的花园，请告之我，我将以最快的速度，携带着诚意和尊重，将它们从您的视野中撤去。
