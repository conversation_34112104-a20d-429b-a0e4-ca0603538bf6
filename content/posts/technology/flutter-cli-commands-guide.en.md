---
title: "Essential Flutter CLI Commands Guide for Developers"
slug: "flutter-cli-commands-guide"
description: "Comprehensive Flutter command-line tools guide covering development, building, debugging, dependency management, and practical tips for all stages"
tags: ["Flutter", "Mobile Development", "Command Line", "Tools"]
keywords: ["Flutter Commands", "Mobile Development", "CLI Tools", "Development Guide"]
categories: ["Technology"]
authors: "wenhao"
date: "2025-01-23"
status: "Published"
image: "https://image.wenhaofree.com/2025/05/9240d7d9a83d2cdc0381f3160780d21b.png"
---

<!-- truncate -->

Flutter provides rich command-line tools to streamline the development process. This article will detail various Flutter CLI commands to help developers improve their workflow efficiency.

## Environment Check & Configuration

### System Diagnostics
```bash
# Check Flutter installation status and system configuration
flutter doctor

# Detailed check (shows all component statuses)
flutter doctor -v

# Check specific platform configuration
flutter doctor --android-licenses
```

### Version Management
```bash
# Check current Flutter version
flutter --version

# List all available channels
flutter channel

# Switch to stable channel
flutter channel stable

# Switch to beta channel
flutter channel beta

# Update Flutter to latest version
flutter upgrade

# Downgrade to previous version
flutter downgrade
```

## Project Creation & Management

### Creating New Projects
```bash
# Create a new Flutter app
flutter create my_app

# Create with specific organization
flutter create --org com.example my_app

# Create with specific platforms
flutter create --platforms android,ios my_app

# Create a package
flutter create --template=package my_package

# Create a plugin
flutter create --template=plugin my_plugin
```

### Project Structure
```bash
# Generate platform-specific code
flutter create --platforms web .

# Add platform support to existing project
flutter create --platforms macos,linux,windows .
```

## Development & Running

### Running Applications
```bash
# Run on connected device
flutter run

# Run in debug mode (default)
flutter run --debug

# Run in profile mode
flutter run --profile

# Run in release mode
flutter run --release

# Run on specific device
flutter run -d device_id

# Run with hot reload disabled
flutter run --no-hot-reload

# Run with specific target file
flutter run -t lib/main_dev.dart
```

### Device Management
```bash
# List all connected devices
flutter devices

# List all available emulators
flutter emulators

# Launch specific emulator
flutter emulators --launch emulator_id

# Run on Chrome (web)
flutter run -d chrome

# Run on specific iOS simulator
flutter run -d "iPhone 14 Pro"
```

## Building & Compilation

### Android Builds
```bash
# Build APK (debug)
flutter build apk

# Build APK (release)
flutter build apk --release

# Build APK for specific architecture
flutter build apk --target-platform android-arm64

# Build App Bundle (recommended for Play Store)
flutter build appbundle

# Build with obfuscation
flutter build apk --obfuscate --split-debug-info=debug-info/
```

### iOS Builds
```bash
# Build iOS app
flutter build ios

# Build iOS app for release
flutter build ios --release

# Build without code signing
flutter build ios --no-codesign

# Build for simulator
flutter build ios --simulator
```

### Web Builds
```bash
# Build web app
flutter build web

# Build with specific renderer
flutter build web --web-renderer canvaskit

# Build with specific base href
flutter build web --base-href /my-app/
```

### Desktop Builds
```bash
# Build for macOS
flutter build macos

# Build for Windows
flutter build windows

# Build for Linux
flutter build linux
```

## Testing & Quality Assurance

### Running Tests
```bash
# Run all tests
flutter test

# Run specific test file
flutter test test/widget_test.dart

# Run tests with coverage
flutter test --coverage

# Run integration tests
flutter drive --target=test_driver/app.dart
```

### Code Analysis
```bash
# Analyze code for issues
flutter analyze

# Format code
flutter format .

# Format specific file
flutter format lib/main.dart

# Check formatting without applying
flutter format --dry-run .
```

## Dependency Management

### Package Operations
```bash
# Get dependencies
flutter pub get

# Update dependencies
flutter pub upgrade

# Add a new dependency
flutter pub add package_name

# Add a dev dependency
flutter pub add --dev package_name

# Remove a dependency
flutter pub remove package_name

# Show dependency tree
flutter pub deps

# Check for outdated packages
flutter pub outdated
```

### Package Publishing
```bash
# Validate package before publishing
flutter pub publish --dry-run

# Publish package
flutter pub publish

# Check package score
flutter pub points
```

## Debugging & Profiling

### Debug Tools
```bash
# Open Flutter Inspector
flutter inspector

# Attach debugger to running app
flutter attach

# Attach to specific device
flutter attach -d device_id

# Enable performance overlay
flutter run --enable-performance-overlay
```

### Performance Analysis
```bash
# Profile app performance
flutter run --profile

# Generate performance trace
flutter drive --profile --trace-startup

# Analyze app size
flutter build apk --analyze-size

# Build with tree shaking disabled
flutter build apk --no-tree-shake-icons
```

## Advanced Commands

### Clean & Reset
```bash
# Clean build artifacts
flutter clean

# Clean and get dependencies
flutter clean && flutter pub get

# Reset Flutter installation
flutter doctor --reset
```

### Configuration
```bash
# Enable/disable specific platforms
flutter config --enable-web
flutter config --enable-macos-desktop
flutter config --enable-linux-desktop
flutter config --enable-windows-desktop

# Configure analytics
flutter config --no-analytics

# Set custom Android SDK path
flutter config --android-sdk /path/to/android/sdk
```

### Logs & Debugging
```bash
# View device logs
flutter logs

# Clear device logs
flutter logs --clear

# View logs for specific device
flutter logs -d device_id

# Enable verbose logging
flutter run -v
```

## Useful Shortcuts & Tips

### Hot Reload & Restart
While `flutter run` is active:
- `r` - Hot reload
- `R` - Hot restart
- `h` - List available commands
- `q` - Quit

### Environment Variables
```bash
# Set Flutter root
export FLUTTER_ROOT=/path/to/flutter

# Set Android SDK path
export ANDROID_SDK_ROOT=/path/to/android/sdk

# Enable verbose logging
export FLUTTER_VERBOSE=true
```

### Batch Operations
```bash
# Clean and rebuild everything
flutter clean && flutter pub get && flutter build apk

# Run tests and analyze code
flutter test && flutter analyze

# Format and analyze code
flutter format . && flutter analyze
```

## Troubleshooting Common Issues

### Permission Issues
```bash
# Fix Android license issues
flutter doctor --android-licenses

# Reset Flutter configuration
flutter doctor --reset

# Clear Flutter cache
flutter clean
```

### Build Issues
```bash
# Clean and rebuild
flutter clean
flutter pub get
flutter build apk

# Reset Gradle wrapper
cd android && ./gradlew clean && cd ..

# Update dependencies
flutter pub upgrade --major-versions
```

### Performance Issues
```bash
# Build with optimization
flutter build apk --release --shrink

# Analyze bundle size
flutter build apk --analyze-size

# Profile memory usage
flutter run --profile --trace-skia
```

## Best Practices

### 1. Regular Maintenance
```bash
# Weekly routine
flutter doctor
flutter pub outdated
flutter analyze
```

### 2. Version Control
```bash
# Before committing
flutter format .
flutter analyze
flutter test
```

### 3. CI/CD Integration
```bash
# Typical CI script
flutter doctor
flutter pub get
flutter test
flutter analyze
flutter build apk --release
```

### 4. Multi-Platform Development
```bash
# Test on all platforms
flutter run -d android
flutter run -d ios
flutter run -d chrome
flutter run -d macos
```

## Conclusion

Mastering Flutter CLI commands significantly improves development efficiency. From project creation to deployment, these commands cover the entire development lifecycle. Regular practice with these commands will make you a more productive Flutter developer.

Remember to:
- Use `flutter doctor` regularly to check your environment
- Keep your Flutter SDK updated
- Leverage hot reload for faster development
- Use proper build configurations for different environments
- Analyze and test your code regularly

Happy Flutter development! 🚀
