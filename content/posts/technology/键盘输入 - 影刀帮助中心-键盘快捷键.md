---
slug: Keyboard-Input---Shadow-Blade-Help-Center---Keyboard-Shortcuts
title: 键盘输入 - 影刀帮助中心-键盘快捷键
date: 2023-11-15
authors: wenhao
tags: ['General']
keywords: ['Default']
---
https://www.yingdao.com/yddoc/language/zh-cn/%E6%8C%87%E4%BB%A4%E6%96%87%E6%A1%A3/%E9%BC%A0%E6%A0%87%E9%94%AE%E7%9B%98/%E9%94%AE%E7%9B%98%E8%BE%93%E5%85%A5.html?source={tab} 

![Image](https://prod-files-secure.s3.us-west-2.amazonaws.com/b0012720-ccd1-41ef-9ca9-02f55a45f30f/5f19db49-4786-4a59-92c3-fbcf7ec01554/45a9043c7926d23457e3a6418b5acd57.png?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Content-Sha256=UNSIGNED-PAYLOAD&X-Amz-Credential=AKIAT73L2G45HZZMZUHI%2F20231125%2Fus-west-2%2Fs3%2Faws4_request&X-Amz-Date=20231125T072007Z&X-Amz-Expires=3600&X-Amz-Signature=051d3e5e7df024b0848ee99d26c6e513fb6c8db8764fc0559ec1a7d05add541a&X-Amz-SignedHeaders=host&x-id=GetObject)
键盘的输入可以分为两种模式，普通模式和特殊按键模式。通过勾选高级->输入内容包含特殊按键来使用特殊按键模式，不勾选则使用普通模式，默认为特殊按键模式。 
![Image](https://prod-files-secure.s3.us-west-2.amazonaws.com/b0012720-ccd1-41ef-9ca9-02f55a45f30f/2383a9f9-eb96-4bd6-80bb-a640aa086936/269ffbf1dd528c54cfaa97b3c5de02fb.png?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Content-Sha256=UNSIGNED-PAYLOAD&X-Amz-Credential=AKIAT73L2G45HZZMZUHI%2F20231125%2Fus-west-2%2Fs3%2Faws4_request&X-Amz-Date=20231125T072007Z&X-Amz-Expires=3600&X-Amz-Signature=8ff1d530108069ed5140c79153268d0ec5b09ea52720ecf2a410dabcd2e60c6c&X-Amz-SignedHeaders=host&x-id=GetObject)
### 普通模式

普通模式，即一般意义上的键盘输入，将文本框中的内容逐字符地填入激活窗口； 
### 特殊按键模式

特殊按键模式，即可输入特殊的按键，包括快捷键（ctrl+a等）和按键特殊操作（持续按下shift键不弹起等）。 
功能按键：对应功能性的按键，比如Ctrl、Win等 
该模式下，普通字符的输入（除了六个符号外，见下方特别注意）和普通模式相同，功能按键的输入除了Alt、Win、Shift、Ctrl四个不同的功能按键可以直接用!、#、+、^这四个符号表示外，其他一般用`{XXXX}`格式表示，比如使用`{ENTER}`表示回车键等。 
### 常用特殊字符列表

更多特殊字符列表查看... 
### 插入键盘符号

![Image](https://prod-files-secure.s3.us-west-2.amazonaws.com/b0012720-ccd1-41ef-9ca9-02f55a45f30f/a7e4291c-304c-42cc-a3f0-f9a152688578/c673594f4c330641aaf26a4383f12648.png?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Content-Sha256=UNSIGNED-PAYLOAD&X-Amz-Credential=AKIAT73L2G45HZZMZUHI%2F20231125%2Fus-west-2%2Fs3%2Faws4_request&X-Amz-Date=20231125T072007Z&X-Amz-Expires=3600&X-Amz-Signature=12de6f1ca02c23489edb9f767347d75c027a6de508bebdc9346b8b58554ad760&X-Amz-SignedHeaders=host&x-id=GetObject)
除了通过手动输入的方式输入特殊符号，还使用插入键盘符号这一功能进行快捷输入，点击插入键盘符号后，会弹出虚拟键盘，通过虚拟键盘可直接输入特殊符号。 
## 两种输入方式

<!-- truncate -->

键盘的输入方式可以分为两种，普通方式和驱动方式。通过勾选【 高级 】->【 驱动输入 】来使用驱动输入方式，不勾选则使用普通方式，默认为普通方式。 
### 普通方式

普通方式，即通过windows键盘消息的方式来模拟按键输入，推荐优先使用普通模式，在输入网上银行密码场景中，普通方式输入失败的情况下，可尝试使用驱动输入方式。 
### 驱动方式

驱动方式，即使用驱动来模拟按键输入，一般用于网银密码框输入场景或其它普通输入方式无法输入的场景。通过勾选【 高级 】->【 驱动输入 】来使用驱动输入方式。 
虚拟键盘驱动安装请查看... 
![Image](https://prod-files-secure.s3.us-west-2.amazonaws.com/b0012720-ccd1-41ef-9ca9-02f55a45f30f/b3ef4415-1b32-486e-9dc6-d5cbf38782c4/4d66c8620b33430c020099deea0eb6c6.png?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Content-Sha256=UNSIGNED-PAYLOAD&X-Amz-Credential=AKIAT73L2G45HZZMZUHI%2F20231125%2Fus-west-2%2Fs3%2Faws4_request&X-Amz-Date=20231125T072007Z&X-Amz-Expires=3600&X-Amz-Signature=6f146de1a814dd23dc5cf6b6abc70782c1e8afc7a0d540c711f9a06a6d384505&X-Amz-SignedHeaders=host&x-id=GetObject)
## 强制加载美式键盘

<!-- truncate -->

存在不常见的输入法切换英文输入状态不成功的情况，需指定强制加载美式键盘(ENG)，确保模拟输入不受中文输入法影响 
## 延时处理

<!-- truncate -->

![Image](https://prod-files-secure.s3.us-west-2.amazonaws.com/b0012720-ccd1-41ef-9ca9-02f55a45f30f/161b020a-4dea-4a07-a7ad-ed871744d43c/b4f516adfb3032ce1f81f231ef20435f.png?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Content-Sha256=UNSIGNED-PAYLOAD&X-Amz-Credential=AKIAT73L2G45HZZMZUHI%2F20231125%2Fus-west-2%2Fs3%2Faws4_request&X-Amz-Date=20231125T072007Z&X-Amz-Expires=3600&X-Amz-Signature=07fc2b66e5ebc7f6574efc0dbec06e7c6ee8e2550b7875eb2649f17228d5d1e9&X-Amz-SignedHeaders=host&x-id=GetObject)
键盘输入提供两种延时处理 
一种是按键之间的输入间隔，两次按键输入的间隔时间，若输入内容出错可适当延长； 
一种是文本内容输入完之后执行延迟，即指令执行完成后的等待时间。 
## 使用示例

<!-- truncate -->

![Image](https://prod-files-secure.s3.us-west-2.amazonaws.com/b0012720-ccd1-41ef-9ca9-02f55a45f30f/80229aab-2818-41a8-a7db-8e1d1a926c8b/E994AEE79B98E8BE93E585A5-03.png?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Content-Sha256=UNSIGNED-PAYLOAD&X-Amz-Credential=AKIAT73L2G45HZZMZUHI%2F20231125%2Fus-west-2%2Fs3%2Faws4_request&X-Amz-Date=20231125T072007Z&X-Amz-Expires=3600&X-Amz-Signature=944f7e38fb157b18a2c69adb352ee6efc0f82b101e7bc758c688dd998d3ee0b2&X-Amz-SignedHeaders=host&x-id=GetObject)
![Image](https://prod-files-secure.s3.us-west-2.amazonaws.com/b0012720-ccd1-41ef-9ca9-02f55a45f30f/83369e9f-b002-4b16-b7da-f5321b9829e3/E994AEE79B98E8BE93E585A5-04.gif?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Content-Sha256=UNSIGNED-PAYLOAD&X-Amz-Credential=AKIAT73L2G45HZZMZUHI%2F20231125%2Fus-west-2%2Fs3%2Faws4_request&X-Amz-Date=20231125T072007Z&X-Amz-Expires=3600&X-Amz-Signature=690c42ed772035f26b01f6576290e9163941cc9c05df9a3db2c68b642da10ea4&X-Amz-SignedHeaders=host&x-id=GetObject)
此流程执行逻辑 ：执行【 获取窗口对象 】指令获取指定窗口 --> 使用【 键盘输入 】指令将文本内容发送给当前激活的窗口 --> 填写文本并`{ENTER}`换行 
## 附：驱动输入不支持的网上银行及解决方案

<!-- truncate -->

### 不支持的网上银行

- 农业银行
- 民生银行
- 平安银行
- 上海银行
### 支持的网上银行

- 国有大型商业银行
- 交通银行
- 中国银行
- 建设银行（没有安全控件）
- 工商银行
- 邮政储蓄银行
- 股份制商业银行
- 光大银行
- 中信银行
- 华夏银行
- 广发银行
- 兴业银行
- 浙商银行
- 渤海银行
- 恒丰银行（没有安全控件）
- 城市商业银行
- 江苏银行
- 南京银行
- 杭州银行
- 。。。
### 解决方案

部分网上银行限制了输入方式，仅允许真实的键盘设备输入密码，影刀提供网银安全控件设备进行按键输入。 
### 使用方法

- 联系影刀官方人员获取网银安全控件设备
- 将网银安全控件设备插在电脑上
- 在应用中安装【网银安全控件输入】指令集
![Image](https://prod-files-secure.s3.us-west-2.amazonaws.com/b0012720-ccd1-41ef-9ca9-02f55a45f30f/c3c8b08d-916c-407a-a8aa-5f83ca757dd6/259b95721cafc7c2d491ffae516253b7.png?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Content-Sha256=UNSIGNED-PAYLOAD&X-Amz-Credential=AKIAT73L2G45HZZMZUHI%2F20231125%2Fus-west-2%2Fs3%2Faws4_request&X-Amz-Date=20231125T072009Z&X-Amz-Expires=3600&X-Amz-Signature=bdb6f31af8ef70376e8eaad9bf51ce7e00a48f10e3dbaee9d16e85e0bea04783&X-Amz-SignedHeaders=host&x-id=GetObject)
- 若电脑上没有插入网银安全控件设备并调用了指令，运行时会报错【 未识别到网银安全控件设备，请您检查后重试。】
![Image](https://prod-files-secure.s3.us-west-2.amazonaws.com/b0012720-ccd1-41ef-9ca9-02f55a45f30f/cb1ca173-0eb4-41ed-8395-ab6c77daf617/4636c8dda385cab416311309d0327c4a.png?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Content-Sha256=UNSIGNED-PAYLOAD&X-Amz-Credential=AKIAT73L2G45HZZMZUHI%2F20231125%2Fus-west-2%2Fs3%2Faws4_request&X-Amz-Date=20231125T072010Z&X-Amz-Expires=3600&X-Amz-Signature=e541e9c7edfec5dd1eea38fa041f3f720f865085db58eb22b75cf0e1b6e7153e&X-Amz-SignedHeaders=host&x-id=GetObject)
- 调用指令输入密码
![Image](https://prod-files-secure.s3.us-west-2.amazonaws.com/b0012720-ccd1-41ef-9ca9-02f55a45f30f/d7da8fa4-33ef-4b6f-96c3-53f8206d071a/62b6c3dc87bdfd1f26f8fed74a6dea61.png?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Content-Sha256=UNSIGNED-PAYLOAD&X-Amz-Credential=AKIAT73L2G45HZZMZUHI%2F20231125%2Fus-west-2%2Fs3%2Faws4_request&X-Amz-Date=20231125T072010Z&X-Amz-Expires=3600&X-Amz-Signature=9f9645ae3c2ccdf2c28c8d8a20678d3da01cd353bf02c8692d055f383a8928ff&X-Amz-SignedHeaders=host&x-id=GetObject)
- 输入效果
![Image](https://prod-files-secure.s3.us-west-2.amazonaws.com/b0012720-ccd1-41ef-9ca9-02f55a45f30f/3bec34f8-e82a-47ca-9e1e-e7b23b9629e6/300b414cdc5682250d526a90583d7aec.png?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Content-Sha256=UNSIGNED-PAYLOAD&X-Amz-Credential=AKIAT73L2G45HZZMZUHI%2F20231125%2Fus-west-2%2Fs3%2Faws4_request&X-Amz-Date=20231125T072010Z&X-Amz-Expires=3600&X-Amz-Signature=b3611be3d447048fbebbb966f72ddd16244bdd6f5440640b71788aef0987aa60&X-Amz-SignedHeaders=host&x-id=GetObject)



 > 在遵循创作的康庄大道上，若我的文字不慎踏入了他人的花园，请告之我，我将以最快的速度，携带着诚意和尊重，将它们从您的视野中撤去。
