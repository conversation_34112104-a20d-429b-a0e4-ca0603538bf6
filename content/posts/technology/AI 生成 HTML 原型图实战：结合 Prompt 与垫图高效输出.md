---
authors: wenhao
categories:
- AI技术
date: 2025-03-30
image: https://image.wenhaofree.com/2025/05/d97f6b2b2f58f48878b30941446b2809.png
keywords:
- AI原型设计
- HTML原型
- Claude
- Prompt工程
- 垫图
- UI生成
- 低代码
- 前端
slug: ai-html-prototype-guide
tags:
- AI
- Prototyping
- HTML
- Claude
- Prompt Engineering
- UI/UX
- Low-Code
- 前端开发
- 原型设计
title: AI 生成 HTML 原型图实战：结合 Prompt 与垫图高效输出
---

<!-- truncate -->

# AI 生成 HTML 原型图实战：结合 Prompt 与垫图高效输出

利用 AI 大模型（如 Claude 3.7）的能力，结合详细的需求描述（Prompt）和视觉参考图（垫图），可以快速生成可交互的 HTML 原型图，极大地提升前端开发和 UI 设计的效率。本文将介绍这一核心思想，并以一个具体案例展示如何操作。

<img src="https://image.wenhaofree.com/2025/05/d97f6b2b2f58f48878b30941446b2809.png"/>

## 核心流程

基本思路是将清晰的需求和视觉风格通过特定方式输入给 AI，让其直接输出代码：

1.  **编写详细的需求 Prompt**：将产品的功能需求、交互逻辑、技术规范、视觉风格等尽可能详细地描述清楚。
2.  **准备视觉参考图（垫图）**：提供一张或多张能够体现期望视觉风格、布局或特定元素的图片。
3.  **输入给 AI 模型**：将 Prompt 和垫图一起输入给支持图文理解的大模型（如 Claude 3.7）。
4.  **生成与迭代**：AI 输出初步的 HTML/CSS/JS 代码。根据输出结果，通过进一步的 Prompt 指令进行反复修改和优化，直至满意。

## 实战案例：设计"智能天气穿搭助手"小程序原型

以下是结合 Prompt 和垫图，让 AI 生成小程序 HTML 原型的具体示例。

### 1. 详细的需求 Prompt

```prompt
**项目需求说明书：智能天气穿搭助手小程序**

**一、项目概述**
开发一款集成实时天气数据与AI穿搭建议的微信小程序，通过个性化推荐提升用户日常生活效率。请基于以下要求，生成包含 HTML, CSS, 和基础 JavaScript 的高保真、可交互原型代码。

**二、功能需求**

1.  **导航架构**
    *   底部双 Tab 结构（今日推荐 / 我的），使用微信小程序原生样式。
    *   页面顶部包含模拟的状态栏（显示时间、信号、电量等，模拟 iOS 风格）。
    *   整体交互遵循微信小程序设计规范。

2.  **今日推荐 (OOTD) 主界面**
    *   信息卡片流式布局。
    *   **天气卡片组**（包含今日天气和近 7 日天气预报）：
        *   显示动态天气图标（至少支持晴、多云、雨、雪、阴等基础状态）。
        *   用图表（如曲线图）展示今日及未来几日温度变化趋势。
        *   包含紫外线指数、湿度、风速等次要信息。
    *   **穿搭推荐卡片**：
        *   提供风格选择器（区分男/女）。
        *   包含一个"AI 生成今日穿搭"按钮，点击后显示加载状态。
        *   点击按钮后，弹出一个模态窗口（Modal）显示推荐结果：
            *   包含 Markdown 格式的穿搭建议文字描述。
            *   包含一个图片轮播区域（Carousel），展示推荐的服装搭配图片，支持手势滑动切换。
            *   提供分享按钮。

3.  **用户配置系统（"我的"页面及首次引导）**
    *   **首次启动引导**：
        *   通过简洁的多步表单引导用户完成基本配置（如 3 步）。
        *   提供城市选择功能，支持搜索和按拼音首字母快速定位。
        *   尝试通过用户头像或选项让用户确认性别。
    *   **"我的"页面**：
        *   展示用户昵称、头像。
        *   允许用户编辑个人资料（如常驻城市、穿搭风格偏好）。
        *   使用标签形式展示用户的风格偏好（例如，最多选择 5 个）。

**三、技术与视觉要求**

1.  **原型实现标准**
    *   生成可直接在浏览器中运行的 HTML, CSS, JS 代码。
    *   采用响应式布局，优先适配移动端屏幕（如 iPhone 14 Pro 尺寸）。
    *   使用 rem 单位进行尺寸设置（假设 1rem = 16px）。
    *   间距和尺寸遵循 4px 或 8px 的倍数原则。
    *   颜色、字体等样式尽量使用 CSS 变量定义在 `:root` 中。

2.  **代码规范与结构**
    *   尝试将可复用的 UI 元素（如天气卡片、推荐弹窗）封装成独立的 HTML 结构或提供注释说明其可组件化。
    *   使用语义化的 HTML 标签。
    *   CSS 类名清晰表意。
    *   JavaScript 用于处理基本的交互效果（如 Tab 切换、弹窗显示/隐藏、轮播图切换）。

3.  **视觉风格与动效**
    *   **主色调**：科技蓝 (#4A90E2) 与 活力橙 (#FF7F50) 作为强调色。
    *   **字体**：优先使用系统默认字体（如 `system-ui`, `-apple-system`, `BlinkMacSystemFont`, `Segoe UI`, `Roboto`, `Helvetica Neue`, `Arial`, sans-serif）。
    *   **卡片样式**：简洁圆角卡片，带有轻微阴影。
    *   **动效**：
        *   Tab 切换：平滑过渡效果 (约 300ms)。
        *   按钮点击：添加轻微的视觉反馈（如缩放或波纹效果）。
        *   弹窗出现/消失：使用平滑的动画效果（如从底部滑入/滑出）。

**四、数据模拟**
    *   在代码中包含模拟的天气数据和穿搭推荐数据（JavaScript 对象或 JSON 格式），用于填充原型界面。

请根据以上需求和下方提供的视觉参考图（垫图），生成相应的 HTML, CSS, 和 JavaScript 代码。
```

### 2. 视觉参考图（垫图）

提供一张能够体现期望风格的界面图片给 AI 作为参考。

*（这里假设已上传了 `image.png` 作为垫图）*
<img src="https://image.wenhaofree.com/2025/05/d97f6b2b2f58f48878b30941446b2809.png"/>


### 3. 输入 AI 并迭代优化

- 将上述详细的 **Prompt** 和 **垫图** 一起输入到 Claude 3.7 或其他支持图文输入的大模型中。
- 模型会根据理解生成 HTML、CSS 和 JavaScript 代码。
- 检查生成的代码和页面效果，针对不符合预期的地方，通过追问或修改 Prompt 的方式，让 AI 进行调整和优化，直到获得满意的原型效果。

通过这种方式，可以快速将产品想法转化为可视、可交互的前端原型，为后续的设计和开发奠定良好基础。