---
slug: Lightweight-GIF-Recording-Gadget-on-Mac---Kap---<PERSON><PERSON>hui
title: Mac 上轻量 GIF 录屏小工具 - Kap - 知乎
date: 2023-11-14
authors: wenhao
tags: ['General']
keywords: ['Default']
---
https://zhuanlan.zhihu.com/p/23333042 

Mac 电脑上录屏一直是一个非常大的需求，市场上也有非常多的轻量级产品，比如 Pinapps 曾经给大家介绍过的 LICEcap、Recordit 等都很不错。不过我今天还想再给大家推荐一款同样简单好用的 GIF 录屏工具 - Kap。 
启动后 Kap 会常驻在系统状态栏上，打开面板后看到一个红色的框选 icon。点击它就可以在屏幕上进行录制区域的框选。 

<!-- truncate -->


![Image](https://prod-files-secure.s3.us-west-2.amazonaws.com/b0012720-ccd1-41ef-9ca9-02f55a45f30f/425591bf-f11c-455d-bb4a-f937cfd259dc/v2-74e86dac6db12b30026257e7eeedfafd_r.jpg?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Content-Sha256=UNSIGNED-PAYLOAD&X-Amz-Credential=AKIAT73L2G45HZZMZUHI%2F20231125%2Fus-west-2%2Fs3%2Faws4_request&X-Amz-Date=20231125T072012Z&X-Amz-Expires=3600&X-Amz-Signature=bd1b63141aedddf108ae75006218df2e253f58082e3de159fa5d4bc72af36e11&X-Amz-SignedHeaders=host&x-id=GetObject)
框定的区域会用虚线标识出来，如果想要调整录制区域可以使用鼠标进行拖拽、拉伸来调整。 
![Image](https://prod-files-secure.s3.us-west-2.amazonaws.com/b0012720-ccd1-41ef-9ca9-02f55a45f30f/4a3f06b8-303c-4622-9645-7687bbe4833b/v2-9926be62adcca735ab3862163e65a069_r.jpg?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Content-Sha256=UNSIGNED-PAYLOAD&X-Amz-Credential=AKIAT73L2G45HZZMZUHI%2F20231125%2Fus-west-2%2Fs3%2Faws4_request&X-Amz-Date=20231125T072012Z&X-Amz-Expires=3600&X-Amz-Signature=d9c84ac47ac6b54914d9ded7a97a704f546a47816b8d248518ae78006dd370ea&X-Amz-SignedHeaders=host&x-id=GetObject)
如果想要预设好 GIF 的长宽尺寸，我们可以点击面板上的设置按钮将面板展开。在这里我们可以对长宽比、默认尺寸进行设置。比如我想要录制 GIF 放到公众号文章中，提前设定好尺寸省去后期调整。 
![Image](https://prod-files-secure.s3.us-west-2.amazonaws.com/b0012720-ccd1-41ef-9ca9-02f55a45f30f/7aba5156-9bb2-4fff-8482-f15383075fb0/v2-99c806d4ba9af49c4a8a32bd6c1670e9_r.jpg?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Content-Sha256=UNSIGNED-PAYLOAD&X-Amz-Credential=AKIAT73L2G45HZZMZUHI%2F20231125%2Fus-west-2%2Fs3%2Faws4_request&X-Amz-Date=20231125T072012Z&X-Amz-Expires=3600&X-Amz-Signature=b1fae3d01becd50fb59456103b56663f35902cc8e15419632228566e4742c587&X-Amz-SignedHeaders=host&x-id=GetObject)
调整好区域后，点击面板上的红色按钮就可以进行录制了。录制好再次点击系统菜单栏上的 icon 就可以了。 
![Image](https://prod-files-secure.s3.us-west-2.amazonaws.com/b0012720-ccd1-41ef-9ca9-02f55a45f30f/da5a3527-13bd-4196-85d8-46a752847bd5/v2-38014287a77d0b4947980f61f0089c62_r.jpg?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Content-Sha256=UNSIGNED-PAYLOAD&X-Amz-Credential=AKIAT73L2G45HZZMZUHI%2F20231125%2Fus-west-2%2Fs3%2Faws4_request&X-Amz-Date=20231125T072012Z&X-Amz-Expires=3600&X-Amz-Signature=96b068d1f65a21b2d011b7ab7641112f3bf4b1e0bbe2fa2665824db91260e515&X-Amz-SignedHeaders=host&x-id=GetObject)
完成后 Kap 会自动调取刚才的录屏进行回放，我们可以对动画的帧数进行设置，应用只提供了15和30两个选项，比 LICEcap 的自定义还是要稍弱一些。 
这里还可以对 GIF 图的尺寸按照设定的长宽比进行缩放，完成后点击 Save 就可以生成 GIF 进行使用了。 
![Image](https://prod-files-secure.s3.us-west-2.amazonaws.com/b0012720-ccd1-41ef-9ca9-02f55a45f30f/d451158d-27eb-408e-b094-ec9e7538ce73/v2-66d06c393d820a347ecbdd7e8bf066d5_r.jpg?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Content-Sha256=UNSIGNED-PAYLOAD&X-Amz-Credential=AKIAT73L2G45HZZMZUHI%2F20231125%2Fus-west-2%2Fs3%2Faws4_request&X-Amz-Date=20231125T072012Z&X-Amz-Expires=3600&X-Amz-Signature=2bbf2dbbb71c0ab68f4691c4cd9a01fbbca03f61af5e2156fc6d20c99e80575f&X-Amz-SignedHeaders=host&x-id=GetObject)
相比较而言 Kap 算是一个非常容易上手而且还算好用的 GIF 录屏工具，日常普通的使用完全可以胜任。但如果你需要对动画帧数细节的调整，LICEcap 依旧是更好的选择，只不过易用性和颜值上差了不少。 
Kap 是一款开源产品，所以它是免费的。想要试用的同学可以到下方地址进行下载： 
PinDesign 会员计划： 
最近在微信某个设计群里,有几位设计师在讨论原型设计工具到底应该是去 学 Frame、Principle 还是 Flinto,或者说是三个都应该去学。 
说实话，我并不认为这是一个好的现象。设计工具它只是工具，是用来将我们的设计思路表达出来的一个手段而已。过多的将注意力放在软件工具上其实有些偏离了一个设计师本应有的目标 - 做设计。 
作为一个研究各类软件十多年的设计师，我想在本期周刊「设计师真的需要学会所有设计工具吗」和大家分享一下这些年我对设计工具的一些想法。 
https://wap.koudaitong.com/v2/showcase/promocode/fetch?alias=hc4o9ht  (二维码自动识别) 



 > 在遵循创作的康庄大道上，若我的文字不慎踏入了他人的花园，请告之我，我将以最快的速度，携带着诚意和尊重，将它们从您的视野中撤去。
