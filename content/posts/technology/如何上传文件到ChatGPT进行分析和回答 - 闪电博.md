---
slug: How-to-Upload-Files-to-ChatGPT-for-Analysis-and-Answering---Lightning
title: 如何上传文件到ChatGPT进行分析和回答 - 闪电博
date: 2023-11-07
authors: wenhao
tags: ['General']
keywords: ['Default']
---
https://www.wbolt.com/how-upload-document-chatgpt.html 

![Image](https://prod-files-secure.s3.us-west-2.amazonaws.com/b0012720-ccd1-41ef-9ca9-02f55a45f30f/075aa268-47e4-450a-b631-6c4d28ae7e55/how-upload-document-chatgpt.jpg?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Content-Sha256=UNSIGNED-PAYLOAD&X-Amz-Credential=AKIAT73L2G45HZZMZUHI%2F20231125%2Fus-west-2%2Fs3%2Faws4_request&X-Amz-Date=20231125T072056Z&X-Amz-Expires=3600&X-Amz-Signature=5600501603a48a302facb6fd2fa27f06fdcb0c2125cf83b49472188dbbf1e2fd&X-Amz-SignedHeaders=host&x-id=GetObject)

<!-- truncate -->

OpenAI一直在为ChatGPT增加一些有趣的功能，包括对网页浏览和 插件 的支持。然而，仍然没有办法原生上传一个文件，并从其上下文中提问。当然，一些用户可以 在他们的数据上训练人工智能聊天机器人 ，但不是每个人都有理解能力来设置工具和库。因此，如果你正在寻找一种简单的方法来上传文件到ChatGPT，本教程将帮助你。我们已经包括了五种不同的方法来上传PDF、Word、Excel和其他文件到ChatGPT。 
- 使用免费Chrome扩展上传文件到ChatGPT
- 使用第三方网站向ChatGPT上传PDF文件
- 使用基于ChatGPT的Bing AI侧边栏分析文档
- 使用ChatGPT插件分析PDF或文本文件
- 使用BookGPT上传文件到ChatGPT
### 使用免费Chrome扩展上传文件到ChatGPT

我们知道，OpenAI还没有添加原生功能来上传文件和文档到ChatGPT。然而，使用一个很酷的 Chrome浏览器ChatGPT扩展 ，你可以将各种类型的文件上传到ChatGPT，而且工作得非常完美。该扩展名为ChatGPT File Uploader Extended，它可以让你上传几种文件格式，包括TXT、JS、PY、HTML、CSS、JSON、CSV、MD、TS、TSX、JSX、PDF、DOC、DOCX、XLS、XLSX和ZIP。要了解它的工作原理，请遵循以下步骤。 
1. 首先，从 这个链接 安装 ChatGPT File Uploader Extended 扩展。它的使用是完全免费的，可以与任何基于Chromium的浏览器一起使用。 
![Image](https://prod-files-secure.s3.us-west-2.amazonaws.com/b0012720-ccd1-41ef-9ca9-02f55a45f30f/46178b2e-6038-42ca-936f-2e938c633b42/Screenshot-2023-05-24-5.53.15-PM.jpg?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Content-Sha256=UNSIGNED-PAYLOAD&X-Amz-Credential=AKIAT73L2G45HZZMZUHI%2F20231125%2Fus-west-2%2Fs3%2Faws4_request&X-Amz-Date=20231125T072056Z&X-Amz-Expires=3600&X-Amz-Signature=d5b20453d4e9bedfc441177ebcbdbbc1c2e9dd087f483be142702b35deee56b9&X-Amz-SignedHeaders=host&x-id=GetObject)
ChatGPT File Uploader Extended扩展 
2. 现在，只需打开 ChatGPT网站 ，右下方会出现一个 “ Upload ” 按钮。 
![Image](https://prod-files-secure.s3.us-west-2.amazonaws.com/b0012720-ccd1-41ef-9ca9-02f55a45f30f/eac06866-2ef9-421f-9a0c-e5908b5491ac/Screenshot-2023-05-24-5.53.38-PM.jpg?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Content-Sha256=UNSIGNED-PAYLOAD&X-Amz-Credential=AKIAT73L2G45HZZMZUHI%2F20231125%2Fus-west-2%2Fs3%2Faws4_request&X-Amz-Date=20231125T072056Z&X-Amz-Expires=3600&X-Amz-Signature=ba67438c5d1324a729516ba344d340a90fa75364fd61074d8ee0e37af64cd7e1&X-Amz-SignedHeaders=host&x-id=GetObject)
3. 点击该按钮，选择你想上传的文件。在流行的文件格式中，它支持PDF、DOC、DOCX、XLSX、TXT等。 
![Image](https://prod-files-secure.s3.us-west-2.amazonaws.com/b0012720-ccd1-41ef-9ca9-02f55a45f30f/0f440ad0-a939-4e3e-9f7c-21c5ef185cb1/Screenshot-2023-05-24-5.55.40-PM.jpg?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Content-Sha256=UNSIGNED-PAYLOAD&X-Amz-Credential=AKIAT73L2G45HZZMZUHI%2F20231125%2Fus-west-2%2Fs3%2Faws4_request&X-Amz-Date=20231125T072056Z&X-Amz-Expires=3600&X-Amz-Signature=529893438be9bff9c52481937bac94f60ac2b296424e59ea3ada406900f68495&X-Amz-SignedHeaders=host&x-id=GetObject)
4. 现在它将开始自动处理你的文件。 
![Image](https://prod-files-secure.s3.us-west-2.amazonaws.com/b0012720-ccd1-41ef-9ca9-02f55a45f30f/d27e738a-b26a-4e5f-ba0f-f893320d05d7/Screenshot-2023-05-24-5.56.51-PM.jpg?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Content-Sha256=UNSIGNED-PAYLOAD&X-Amz-Credential=AKIAT73L2G45HZZMZUHI%2F20231125%2Fus-west-2%2Fs3%2Faws4_request&X-Amz-Date=20231125T072056Z&X-Amz-Expires=3600&X-Amz-Signature=6db66607674a04bbe8d60ca080290491a5bb19186b6bfe6715a4fc66e9367c76&X-Amz-SignedHeaders=host&x-id=GetObject)
5. 一段时间后，人工智能聊天机器人将生成一份文件的摘要。 
![Image](https://prod-files-secure.s3.us-west-2.amazonaws.com/b0012720-ccd1-41ef-9ca9-02f55a45f30f/e5c0a481-e74d-4276-9076-b7aaca9c176c/Screenshot-2023-05-24-5.57.26-PM.jpg?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Content-Sha256=UNSIGNED-PAYLOAD&X-Amz-Credential=AKIAT73L2G45HZZMZUHI%2F20231125%2Fus-west-2%2Fs3%2Faws4_request&X-Amz-Date=20231125T072056Z&X-Amz-Expires=3600&X-Amz-Signature=ae7d2defa6fbebd6c3baf1563140fd81d0a45f4bd974d8917e4dac0ada4ac518&X-Amz-SignedHeaders=host&x-id=GetObject)
6. 你现在可以提出问题，ChatGPT将从上传的文件中回答。泰裤辣！ 
![Image](https://prod-files-secure.s3.us-west-2.amazonaws.com/b0012720-ccd1-41ef-9ca9-02f55a45f30f/a437877a-ba1b-4231-92c8-90945406e4fb/Screenshot-2023-05-24-5.58.14-PM.jpg?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Content-Sha256=UNSIGNED-PAYLOAD&X-Amz-Credential=AKIAT73L2G45HZZMZUHI%2F20231125%2Fus-west-2%2Fs3%2Faws4_request&X-Amz-Date=20231125T072056Z&X-Amz-Expires=3600&X-Amz-Signature=ed0060ff7d590a12b38a549712d8b04ba78085c2fd2a9e0c0a002dfd0a4f7672&X-Amz-SignedHeaders=host&x-id=GetObject)
7. 我测试了Word文件、Excel表格、PDF文件等，它都能成功处理。但请记住，它不能处理带有扫描图像的文件。 
![Image](https://prod-files-secure.s3.us-west-2.amazonaws.com/b0012720-ccd1-41ef-9ca9-02f55a45f30f/0b3d1aef-be96-4db1-825e-9478752b4a14/Screenshot-2023-05-24-5.59.15-PM.png?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Content-Sha256=UNSIGNED-PAYLOAD&X-Amz-Credential=AKIAT73L2G45HZZMZUHI%2F20231125%2Fus-west-2%2Fs3%2Faws4_request&X-Amz-Date=20231125T072056Z&X-Amz-Expires=3600&X-Amz-Signature=19a5226beb4c3642509d299e24edf21554cbd2b1a2f6a747050adc051066ed68&X-Amz-SignedHeaders=host&x-id=GetObject)
8. 你也可以点击 “上传” 按钮旁边的 “设置” 图标，如果你有一个大文件，可以增加上传文件的大小。此外，你还可以自定义提示、ZIP文件设置等。 
![Image](https://prod-files-secure.s3.us-west-2.amazonaws.com/b0012720-ccd1-41ef-9ca9-02f55a45f30f/521e3805-9fcc-45cf-a2fd-224c9752665b/Screenshot-2023-05-24-5.59.38-PM.png?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Content-Sha256=UNSIGNED-PAYLOAD&X-Amz-Credential=AKIAT73L2G45HZZMZUHI%2F20231125%2Fus-west-2%2Fs3%2Faws4_request&X-Amz-Date=20231125T072056Z&X-Amz-Expires=3600&X-Amz-Signature=776491abcb380f3117257986b720e0b6592ed5dd9aec33c7acf4e51363490921&X-Amz-SignedHeaders=host&x-id=GetObject)
### 使用第三方网站向ChatGPT上传PDF文件

如果你想上传PDF文件到ChatGPT，你可以看一下 chatpdf.com 。这是一个伟大的服务，使用ChatGPT的API来处理和分析PDF文件。最重要的是，你不需要添加你的API密钥。 
对于免费用户，该服务允许每个PDF文件多达120页（10MB或以下），一天内可上传三个PDF文件。你每天最多可以提出50个问题。如果你选择获得Plus订阅（每月5美元），你可以向这个ChatGPT网站上传每个PDF文件多达2000页。以下是如何使用它。 
1. 前往 chatpdf.com ，点击 “ Drop PDF here “，上传您的PDF文件。 
2. 你也可以使用底部的 “From URL” 链接输入PDF文件的URL。 
![Image](https://prod-files-secure.s3.us-west-2.amazonaws.com/b0012720-ccd1-41ef-9ca9-02f55a45f30f/b7ba0c0f-18d6-47e3-9f41-595bdae8e5e6/Screenshot-2023-05-24-6.00.08-PM.jpg?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Content-Sha256=UNSIGNED-PAYLOAD&X-Amz-Credential=AKIAT73L2G45HZZMZUHI%2F20231125%2Fus-west-2%2Fs3%2Faws4_request&X-Amz-Date=20231125T072056Z&X-Amz-Expires=3600&X-Amz-Signature=64b8a06c3dff07f14f1670c2aff86fac0780218d548c939b0550c2b6db49d0c6&X-Amz-SignedHeaders=host&x-id=GetObject)
3. 它将在几秒钟内处理PDF文件，这取决于文件的大小。 
![Image](https://prod-files-secure.s3.us-west-2.amazonaws.com/b0012720-ccd1-41ef-9ca9-02f55a45f30f/6b4fdc8d-32fe-4304-9bc0-34fa58d73a47/Screenshot-2023-05-24-6.00.39-PM.jpg?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Content-Sha256=UNSIGNED-PAYLOAD&X-Amz-Credential=AKIAT73L2G45HZZMZUHI%2F20231125%2Fus-west-2%2Fs3%2Faws4_request&X-Amz-Date=20231125T072056Z&X-Amz-Expires=3600&X-Amz-Signature=228e8b1dccfbf9d75a3720cdd28f5ba766cd79de74852415c5069e639529c549&X-Amz-SignedHeaders=host&x-id=GetObject)
4. 首先，它将显示该文件的摘要和三个建议的问题。你也可以在下面提出你的问题，并与PDF文件聊天。 
### 使用基于ChatGPT的Bing AI侧边栏分析文档

对于那些不知道的人来说，微软已经在其Edge浏览器中添加了一个由ChatGPT驱动的 Bing侧边栏 （也叫Discover）。这是最好的人工智能工具之一，允许你分析文件，甚至不需要上传文件。只需在Microsoft Edge中打开文件，它就会自动处理该文件。以下是它的工作方式。 
1. 启动Microsoft Edge，按 “Ctrl + O” 打开PDF文件。你也可以打开网页版的Microsoft Office或Google Docs，甚至是一个网页。 
2. 现在，点击Bing侧边栏按钮，开始提问。你可以从摘要开始，它将从该标签中打开的文件中给出一个有背景的答案。 
3. 你可以提出进一步的问题，它将继续从文件本身回答。如果它从网上回答，你也可以点击 “Answer from this page instead”。 
### 使用ChatGPT插件分析PDF或文本文件

如果您订阅了ChatGPT Plus，您现在可以使用最新的GPT-4模型和ChatGPT插件来上传和学习任何种类的文件。您需要启用ChatGPT插件来访问这些文件。以下是您需要遵循的步骤： 
1. 首先，打开 ChatGPT网站 ，按照我们的链接指南， 启用ChatGPT插件 。 
2. 接下来，移动到 “GPT-4” 模型。在这里，点击 “Plugins” 选项。 
3. 现在，使用下拉菜单打开 “ Plugin store “。 
4. 在这里，移动到 “All” 部分，寻找 “AskYourPDF” 插件，并安装它。你也可以尝试链接阅读器插件，其工作原理与此类似。 
5. 接下来，一旦插件安装完毕，从箭头图标上启用 “AskYourPDF”。 
6. 最后，在ChatGPT中输入文件链接，你就可以问任何问题了。 
7. 如果你想上传自己的文件，运行下面的提示语，它会给你一个链接，你可以在那里上传文件。 
upload a document 
8. 打开链接，在这里，你可以上传各种文件格式，包括PDF、DOC、DOCX、TXT、PPT、PPTX、CSV、EPUB和RTF。 
9. 现在它将给你一个文件ID。复制它。这将允许你在ChatGPT的界面上继续与该文件进行聊天。 
10. 只需粘贴文档ID并提出您的问题。它将自动识别您上传的文件，并让您只从该特定文件中提问。 
### 使用BookGPT上传文件到ChatGPT

最后，在Hugging Face上还有一个 BookGPT 项目，可以让你上传一整本书。如果你有一个大文件，这个项目就适合你。它使用ChatGPT的API来处理和分析文件。唯一的缺点是，你需要添加你的OpenAI API密钥。下面是它的工作原理。 
1. 首先，从这里 登录到你的OpenAI账户 。如果你没有账户，请创建一个免费账户。 
2. 然后，点击 “ Create a new secret key “，复制API密钥。如果你已经用完了你的免费API额度，你需要添加一张信用卡来使用它。 
3. 接下来，打开 BookGPT项目 ，在底部粘贴OpenAI的API密钥。 
4. 之后，添加PDF文件的URL或上传本地PDF文件到ChatGPT。现在，请提出您的问题并点击这里的 “Submit”。 
5. 在几秒钟内，文件将被处理，你将得到一个 带有页码引用的答案 。这不是很酷吗？你现在可以继续提出更多的问题。 
### 常见问题

你可以向ChatGPT上传文件吗？ 
OpenAI还没有为ChatGPT的界面增加一个原生选项来上传文件。然而，通过插件支持、API密钥、扩展和其他第三方服务，您确实可以向ChatGPT上传文件。 
您可以将PDF文件载入ChatGPT吗？ 
是的，您可以使用插件加载PDF文件到ChatGPT。除此以外，您可以使用Chrome扩展程序、OpenAI API密钥或由ChatGPT驱动的网站来无缝加载PDF文件。 



 > 在遵循创作的康庄大道上，若我的文字不慎踏入了他人的花园，请告之我，我将以最快的速度，携带着诚意和尊重，将它们从您的视野中撤去。
