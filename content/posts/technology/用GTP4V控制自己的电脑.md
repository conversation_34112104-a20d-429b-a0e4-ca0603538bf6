---
slug: Control-your-computer-with-GTP4V
title: 用GTP4V控制自己的电脑
date: 2023-11-30
authors: wenhao
tags: ['ChatGPT']
keywords: ['ChatGPT4']
---
operating-computer，https://t.co/qXKNbRduXU，这个项目演示了如何让 GPT-4V 来控制自己的电脑，你需要做的就是告诉它完成一个怎样的任务，例如，打开 Google Docs 写一篇文章，然后发布并分享给同事。
<!-- truncate -->
它的 Prompt 写的比较简单，定义了一个可以与机器交互的… https://t.co/AKJAumDIN2" / X undefined 
![Image](https://pbs.twimg.com/media/F__VhPYaoAAZJkf.jpg?format=jpg&name=medium)
 Barret李靖 @Barret_China · 28 11月 2023 · link 🔗
self-operating-computer，https://github.com/OthersideAI/self-operating-computer，这个项目演示了如何让 GPT-4V 来控制自己的电脑，你需要做的就是告诉它完成一个怎样的任务，例如，打开 Google Docs 写一篇文章，然后发布并分享给同事。 
它的 Prompt 写的比较简单，定义了一个可以与机器交互的 DSL，主要包含三种动作：Click/Type/Search，分别对应 mouse_click/keyboard_type/mac_search 几个封装好的系统函数。 
每次程序执行动作时，都会携带任务目标、上一步执行结果以及当前屏幕截图作为上下文，然后将信息传递给 ChatGPT，并让它给出下一步操作指示。 
这个项目中 Prompt 的定制化程度偏高，说明完成复杂的工作还比较有挑战，如果参考之前提到的 Mutil-Agent 方案， 
![Image](https://pbs.twimg.com/media/F__VhPYaoAAZJkf.jpg)



 > 在遵循创作的康庄大道上，若我的文字不慎踏入了他人的花园，请告之我，我将以最快的速度，携带着诚意和尊重，将它们从您的视野中撤去。
