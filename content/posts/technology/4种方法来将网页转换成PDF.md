---
slug: 4-Ways-to-Convert-a-Web-Page-to-PDF
title: 4种方法来将网页转换成PDF
date: 2023-11-11
authors: wenhao
tags: ['General']
keywords: ['Default']
---
https://zh.wikihow.com/%E5%B0%86%E7%BD%91%E9%A1%B5%E8%BD%AC%E6%8D%A2%E6%88%90PDF 

如果你需要保存网页以供日后离线浏览，或是想要分享或打印网页，那就可以考虑把网页转换成PDF文件来简化整个操作。Chrome和Safari浏览器都自带创建PDF文件的工具，你可以通过它们来直接转换网页。如果使用的火狐或IE浏览器，那就要使用额外的软件。“Adobe Acrobat”程序功能最全面，可以获取网页信息，并将它们转换成PDF文件。 
- 
-  
-  1 
- 打开你想要转换成PDF文件的网页。
- 创建网页的PDF副本时，有些元素会被自动更改。这些元素大多都是由网站开发者来操控处理的，往往不在访客的控制范围之内。
-  
- 
以Convert a Webpage to PDF Step 1为标题的图片 
![Image](https://prod-files-secure.s3.us-west-2.amazonaws.com/b0012720-ccd1-41ef-9ca9-02f55a45f30f/a068ff1d-f32c-4269-8199-1bcf1c7193a4/v4-728px-Convert-a-Webpage-to-PDF-Step-1-Version-3.jpg.webp?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Content-Sha256=UNSIGNED-PAYLOAD&X-Amz-Credential=AKIAT73L2G45HZZMZUHI%2F20231125%2Fus-west-2%2Fs3%2Faws4_request&X-Amz-Date=20231125T072026Z&X-Amz-Expires=3600&X-Amz-Signature=42efe414d2da4481e6c0c7337d8c7756cfd6bbe9859fab28715a78210a38d3c9&X-Amz-SignedHeaders=host&x-id=GetObject)
[1]  
- 这种方法仅适用于打印当前浏览的网页，而不会保留网站上链接到其它页面的链接。如果想把整个网站都保存为PDF文件，或是想要百分百精确复制整个网站，而不更改任何网页内容，请参看方法4。
- 
-  
- 点击“Chrome”浏览器的“菜单”按钮，选择“打印”。
-  
-  2
![Image](https://prod-files-secure.s3.us-west-2.amazonaws.com/b0012720-ccd1-41ef-9ca9-02f55a45f30f/9ff82cb9-0bd6-4315-8801-af6f368cb6af/v4-728px-Convert-a-Webpage-to-PDF-Step-2-Version-3.jpg.webp?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Content-Sha256=UNSIGNED-PAYLOAD&X-Amz-Credential=AKIAT73L2G45HZZMZUHI%2F20231125%2Fus-west-2%2Fs3%2Faws4_request&X-Amz-Date=20231125T072026Z&X-Amz-Expires=3600&X-Amz-Signature=2a9dadabb2bcb2c8b913960bc4aa060d9b0591f42ce5b454902463d332448f8d&X-Amz-SignedHeaders=host&x-id=GetObject)
- 
-  
- 点击更改 按钮，选择“另存为PDF”。 它位于“目标”部分中。
-  
-  3
![Image](https://prod-files-secure.s3.us-west-2.amazonaws.com/b0012720-ccd1-41ef-9ca9-02f55a45f30f/b1f792d2-7c80-4a70-b3f5-7bcf89b5c84b/v4-728px-Convert-a-Webpage-to-PDF-Step-3-Version-3.jpg.webp?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Content-Sha256=UNSIGNED-PAYLOAD&X-Amz-Credential=AKIAT73L2G45HZZMZUHI%2F20231125%2Fus-west-2%2Fs3%2Faws4_request&X-Amz-Date=20231125T072026Z&X-Amz-Expires=3600&X-Amz-Signature=dc2cef2044a8b177fdca756e1848dab0973352edc40a73f9b1522eea21d48a48&X-Amz-SignedHeaders=host&x-id=GetObject)
- 
-  
-  4 
- 调整选项。
- 你可以调整部分选项，以便创建PDF文件。 
- 
以Convert a Webpage to PDF Step 4为标题的图片 
![Image](https://prod-files-secure.s3.us-west-2.amazonaws.com/b0012720-ccd1-41ef-9ca9-02f55a45f30f/d156cfdc-c30b-4faf-a6d1-89e433170fa4/v4-728px-Convert-a-Webpage-to-PDF-Step-4-Version-3.jpg.webp?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Content-Sha256=UNSIGNED-PAYLOAD&X-Amz-Credential=AKIAT73L2G45HZZMZUHI%2F20231125%2Fus-west-2%2Fs3%2Faws4_request&X-Amz-Date=20231125T072027Z&X-Amz-Expires=3600&X-Amz-Signature=d69af21d485faac85676080dd913aca1008d9e4e08e9bd08d0e49d6ad33b118e&X-Amz-SignedHeaders=host&x-id=GetObject)
- 点击“布局”下拉菜单，选择“纵向”或“横向”。
- 如果不想在PDF页面的顶部和底部添加日期、标题和地址信息，那就取消勾选“页眉和页脚”选项。
- 勾选“背景颜色和图片”，保留所有背景图案。
- 
-  
-  5 
- 点击保存 
- 按钮。
-  设置文件名和保存路径，保存PDF文件。
以Convert a Webpage to PDF Step 5为标题的图片 
![Image](https://prod-files-secure.s3.us-west-2.amazonaws.com/b0012720-ccd1-41ef-9ca9-02f55a45f30f/743acfb0-e5d0-4558-b2fe-cab8524c8bd4/v4-728px-Convert-a-Webpage-to-PDF-Step-5-Version-3.jpg.webp?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Content-Sha256=UNSIGNED-PAYLOAD&X-Amz-Credential=AKIAT73L2G45HZZMZUHI%2F20231125%2Fus-west-2%2Fs3%2Faws4_request&X-Amz-Date=20231125T072027Z&X-Amz-Expires=3600&X-Amz-Signature=451c0d5d1c9a09418f51320a4b61137da6e6d4b0b1c9171efdc5df70973403fe&X-Amz-SignedHeaders=host&x-id=GetObject)
- 
-  
-  
-  1 
- 打开你想要转换成PDF文件的网页。
- 使用这种方法时，网页上的部分元素会发生变化。这是因为网页开发者能够强制浏览器按照某种特定方式来打印网页。 
- 
![Image](https://prod-files-secure.s3.us-west-2.amazonaws.com/b0012720-ccd1-41ef-9ca9-02f55a45f30f/72d2aef1-9e12-4aee-a889-06b1cb217e18/v4-728px-Convert-a-Webpage-to-PDF-Step-6-Version-3.jpg.webp?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Content-Sha256=UNSIGNED-PAYLOAD&X-Amz-Credential=AKIAT73L2G45HZZMZUHI%2F20231125%2Fus-west-2%2Fs3%2Faws4_request&X-Amz-Date=20231125T072028Z&X-Amz-Expires=3600&X-Amz-Signature=ef235fead87cdfd2709ec8fc937d427ab966ccee553cd1b8f3420dbd15e996e3&X-Amz-SignedHeaders=host&x-id=GetObject)
- 你只能创建当前浏览网页的PDF副本。如果想要创建全部网页副本，或想让副本文件包含网页上的所有元素，请参看方法4。
- 
[2]  
- 
-  
-  
-  2 
- 点击“文件”菜单，选择“导出到PDF”。
- 这种方法需要使用OS X 10.9和更新的系统。如果使用较早版本的系统，点击“文件” → “另存为”，然后选择PDF文件类型。
![Image](https://prod-files-secure.s3.us-west-2.amazonaws.com/b0012720-ccd1-41ef-9ca9-02f55a45f30f/7af311a6-116b-4402-a7a1-47f8f04a8950/v4-728px-Convert-a-Webpage-to-PDF-Step-7-Version-2.jpg.webp?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Content-Sha256=UNSIGNED-PAYLOAD&X-Amz-Credential=AKIAT73L2G45HZZMZUHI%2F20231125%2Fus-west-2%2Fs3%2Faws4_request&X-Amz-Date=20231125T072029Z&X-Amz-Expires=3600&X-Amz-Signature=61848ee76051e6fecfe8f0173e67a2901a51bbaec7ca1da5ab80771714e99853&X-Amz-SignedHeaders=host&x-id=GetObject)
- 
-  
-  
-  3 
- 命名文件，选择保存路径。
- 根据网页大小，创建文件需要一段时间。
![Image](https://prod-files-secure.s3.us-west-2.amazonaws.com/b0012720-ccd1-41ef-9ca9-02f55a45f30f/7e4bdabc-bbe1-476a-8ab0-061058f52131/v4-728px-Convert-a-Webpage-to-PDF-Step-8-Version-3.jpg.webp?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Content-Sha256=UNSIGNED-PAYLOAD&X-Amz-Credential=AKIAT73L2G45HZZMZUHI%2F20231125%2Fus-west-2%2Fs3%2Faws4_request&X-Amz-Date=20231125T072030Z&X-Amz-Expires=3600&X-Amz-Signature=6b458106e836f4bc13d47153314a9a001cedff35cb2eb9bf392886a6c53478a7&X-Amz-SignedHeaders=host&x-id=GetObject)
### “CutePDF”程序（适用于所有Windows浏览器）

下载PDF文件 
- 
-  
-  
-  1 
- 下载“CutePDF”程序。
- IE浏览器和火狐浏览器没有内置工具来创建PDF文件。因此，你需要安装“虚拟打印机”来创建PDF文件。“CutePDF”程序是一款好用又方便的虚拟打印机。 
- 
![Image](https://prod-files-secure.s3.us-west-2.amazonaws.com/b0012720-ccd1-41ef-9ca9-02f55a45f30f/3823a2b2-35b4-4c55-b79a-c3122d9cc526/v4-728px-Convert-a-Webpage-to-PDF-Step-9-Version-4.jpg.webp?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Content-Sha256=UNSIGNED-PAYLOAD&X-Amz-Credential=AKIAT73L2G45HZZMZUHI%2F20231125%2Fus-west-2%2Fs3%2Faws4_request&X-Amz-Date=20231125T072030Z&X-Amz-Expires=3600&X-Amz-Signature=e42240ef0511f39640f5a48309556ee661770878be3b8c53e3c0ee3fae7a5596&X-Amz-SignedHeaders=host&x-id=GetObject)
- 前往cutepdf.com/products/cutepdf/writer.asp，点击“免费下载”和“免费转换器”按钮，下载安装所需的两个程序。
- 这种方法仅适用于将当前浏览的页面创建为PDF文件。如果想要保存整个网站，包括其它链接页面，请参看方法4。
- 
-  
-  
-  2 
- 运行CuteWriter.exe 
- 程序，开始安装“CutePDF”程序。
-  这个安装程序中带有多个浏览器工具栏，在安装过程中，点击取消按钮，然后点击“跳过这个和其它程序”链接。
![Image](https://prod-files-secure.s3.us-west-2.amazonaws.com/b0012720-ccd1-41ef-9ca9-02f55a45f30f/12ff6f00-e43c-48dd-8a39-4ed219737a33/v4-728px-Convert-a-Webpage-to-PDF-Step-10-Version-3.jpg.webp?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Content-Sha256=UNSIGNED-PAYLOAD&X-Amz-Credential=AKIAT73L2G45HZZMZUHI%2F20231125%2Fus-west-2%2Fs3%2Faws4_request&X-Amz-Date=20231125T072031Z&X-Amz-Expires=3600&X-Amz-Signature=c15772dda03eeccf283a4ea9975ba56d3c075703b184155d9f8f09917502463f&X-Amz-SignedHeaders=host&x-id=GetObject)
- 
-  
-  
-  3 
- 完成“CutePDF”设置后，运行converter.exe 
- 程序。
-  在这一部分中，你不需要选择任何选项，也无需担心任何广告软件。这一步会自动完成，并且不会包含任何广告。
![Image](https://prod-files-secure.s3.us-west-2.amazonaws.com/b0012720-ccd1-41ef-9ca9-02f55a45f30f/49907d69-77a1-4e29-aaee-63f7f469ccbb/v4-728px-Convert-a-Webpage-to-PDF-Step-11-Version-3.jpg.webp?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Content-Sha256=UNSIGNED-PAYLOAD&X-Amz-Credential=AKIAT73L2G45HZZMZUHI%2F20231125%2Fus-west-2%2Fs3%2Faws4_request&X-Amz-Date=20231125T072031Z&X-Amz-Expires=3600&X-Amz-Signature=be3d0539027c564523a61735ce36dfc99dd1360ea2f63e849a58b87a867edc8e&X-Amz-SignedHeaders=host&x-id=GetObject)
- 
-  
-  
-  4 
- 打开网页。
- 安装“CutePDF”后，你可以在任意浏览器中创建PDF文件。
![Image](https://prod-files-secure.s3.us-west-2.amazonaws.com/b0012720-ccd1-41ef-9ca9-02f55a45f30f/8ca87361-476e-4541-a60e-860cd099fe03/v4-728px-Convert-a-Webpage-to-PDF-Step-12-Version-3.jpg.webp?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Content-Sha256=UNSIGNED-PAYLOAD&X-Amz-Credential=AKIAT73L2G45HZZMZUHI%2F20231125%2Fus-west-2%2Fs3%2Faws4_request&X-Amz-Date=20231125T072031Z&X-Amz-Expires=3600&X-Amz-Signature=d874c474a113ef06fe16abc2aad28d513a501bb2973467ebfee6bc83a6a814d2&X-Amz-SignedHeaders=host&x-id=GetObject)
- 
-  
-  
-  5 
- 打开“打印”窗口。
- 同时按下Ctrl+P快捷键，打开“打印”窗口，也可以从“文件”菜单或“火狐浏览器菜单”中进入“打印”窗口。 
- 
![Image](https://prod-files-secure.s3.us-west-2.amazonaws.com/b0012720-ccd1-41ef-9ca9-02f55a45f30f/56fbf57f-b755-472f-9ece-bc4d734d7f14/v4-728px-Convert-a-Webpage-to-PDF-Step-13-Version-3.jpg.webp?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Content-Sha256=UNSIGNED-PAYLOAD&X-Amz-Credential=AKIAT73L2G45HZZMZUHI%2F20231125%2Fus-west-2%2Fs3%2Faws4_request&X-Amz-Date=20231125T072032Z&X-Amz-Expires=3600&X-Amz-Signature=aa2f70ef861d13092a396acf917d36fcd11b4ca320ec7d14863b64e86fc3425f&X-Amz-SignedHeaders=host&x-id=GetObject)
- 在火狐浏览器中，你需要点击“打印预览”窗口中的打印按钮。
- 
-  
-  
-  6 
- 打开下拉菜单，显示打印机列表，并从中选择“CutePDF Writer”。
- 点击打印按钮。
![Image](https://prod-files-secure.s3.us-west-2.amazonaws.com/b0012720-ccd1-41ef-9ca9-02f55a45f30f/f7f662b7-cb5c-41f9-9524-f0c2e6c2665c/v4-728px-Convert-a-Webpage-to-PDF-Step-14-Version-3.jpg.webp?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Content-Sha256=UNSIGNED-PAYLOAD&X-Amz-Credential=AKIAT73L2G45HZZMZUHI%2F20231125%2Fus-west-2%2Fs3%2Faws4_request&X-Amz-Date=20231125T072032Z&X-Amz-Expires=3600&X-Amz-Signature=f5b78662c90bcea5e0316a4c695984e8cbdf0e1e6bff5e6c2aa68a688a2703f6&X-Amz-SignedHeaders=host&x-id=GetObject)
- 
-  
-  
-  7 
- 命名并保存PDF文件。
- 几秒钟后会出现“CutePDF保存”窗口，你可以命名文件，然后选择保存路径。
![Image](https://prod-files-secure.s3.us-west-2.amazonaws.com/b0012720-ccd1-41ef-9ca9-02f55a45f30f/8747d1fd-6b84-4498-b9c4-90ebb5e8e683/v4-728px-Convert-a-Webpage-to-PDF-Step-15-Version-3.jpg.webp?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Content-Sha256=UNSIGNED-PAYLOAD&X-Amz-Credential=AKIAT73L2G45HZZMZUHI%2F20231125%2Fus-west-2%2Fs3%2Faws4_request&X-Amz-Date=20231125T072032Z&X-Amz-Expires=3600&X-Amz-Signature=30593c3720baa3c32a99cbd1f73cf8486f6ff8f934279b64e294aa349a8a366a&X-Amz-SignedHeaders=host&x-id=GetObject)
方法 4 
### 专业版Adobe Acrobat程序

下载PDF文件 
- 1 
- 点击“文件”菜单，选择“创建 PDF” → “从网页创建”。
- 你需要安装付费版“Adobe Acrobat”程序，才能使用这种方法来创建PDF文件。你可以保存整个网站，包括服务器上的每个网页，并且保留工作链接的原始格式。
- 2 
- 输入你想要转换的网页地址。
- 如果想要保存整个网站，输入主页地址。如果想要保存网站的一个网页，输入网页对应的地址。
- 3 
- 确定你想要获取网站的内容。
- 你可以选择“仅获取X等级”或“获取整个网站”。 
- 
- 等级1代表仅打开访问地址对应的页面。等级2包括起始页面上包含的所有页面。等级3包括等级2页面上包含的所有页面。根据网站的设计，等级3可能会包含大量的文件。
- 4 
- 勾选选项，让程序停留在选定的网站上。
- 当你处理多等级任务时，程序很可能会跟随网站链接进入其它网站。你可以选择“停留在同一路径”来获取相同域名下的网页，也可以选择“停留在同一个服务器中”来获取网页服务器上的页面。
- 5 
- 点击设置... 
- 按钮，调整PDF设置。
-  这允许你调整页眉页脚和导航书签等设置。
- 6 
- 点击创建 
- 按钮，保存PDF文件。
-  根据获取网站的等级和网站大小，保存文件可能需要几分钟或
- 很长
- 时间。
- 
[3]  
## 相关wikiHows

<!-- truncate -->

如何下载YouTube视频      如何从SIM卡中获取你的手机号码    如何登录Twitter    如何从网站上免费下载视频    如何打开EPUB文件    如何知道别人是否在Whatsapp上把你屏蔽了    如何发送匿名电子邮件    如何绕过YouTube的年龄限制    如何打开XML文件    如何在YouTube中更改国家    如何将照片从iPhone传到电脑    如何在Instagram上隐藏你关注了哪些人    
## 参考

<!-- truncate -->

- 
- ↑
-  
- http://www.cnet.com/how-to/five-ways-to-save-a-web-page/
- 
- 
- ↑
-  
- http://apple.stackexchange.com/questions/124157/in-safari-export-to-pdf-looks-different-then-print-as-pdf-why-is-this
- 
- 
- ↑
-  
- http://www.planetpdf.com/enterprise/article.asp?ContentID=How_To_Create_a_PDF_of_an_Entire_Website&page=1
- 
## 关于本wikiHow

<!-- truncate -->

wikiHow是一个“多人协作写作系统”，因此我们的很多文章都是由多位作者共同创作的。 为了创作这篇文章，志愿作者多次对文章进行了编辑和改进。 这篇文章已经被读过134,441次。 
分类:  计算机与电子产品  |  软件 
其他语言 
English: Convert a Webpage to PDF 
Français: convertir une page internet en PDF 
Español: convertir una página web a PDF 
Bahasa Indonesia: Mengonversi Halaman Web menjadi PDF 
Português: Converter uma Página da Internet para PDF 
Русский: конвертировать веб страницу в PDF 
Italiano: Convertire una Pagina Web in un File PDF 
Tiếng Việt: Chuyển trang web sang tập tin PDF 
ไทย: แปลงหน้าเว็บเป็นไฟล์ PDF 
हिन्दी: वेबपेज (webpage) को पीडीएफ़ (PDF) में बदलें 
한국어: 웹페이지를 PDF로 변환하는 방법 
日本語: ウェブページをPDF形式に変換する 
Türkçe: İnternet Sayfası PDF'ye Nasıl Dönüştürülür 
Nederlands: Een wegpagina opslaan als pdf 
- 打印
本页面已经被访问过134,441次。 



 > 在遵循创作的康庄大道上，若我的文字不慎踏入了他人的花园，请告之我，我将以最快的速度，携带着诚意和尊重，将它们从您的视野中撤去。
