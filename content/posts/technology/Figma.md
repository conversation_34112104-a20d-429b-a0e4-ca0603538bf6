---
slug: Figma-induction
title: Figma基本概念及入门使用
date: 2022-03-16
authors: wenh<PERSON>
tags: [UI, Figma]
keywords: [U<PERSON>,Figma,<PERSON><PERSON><PERSON>,文浩,Marvin]
description: 关于Figma基本概念及入门使用
---

## 初识Figma
![Figma](https://img.wenhaofree.com/blog/001.png)

Figma是一款基于云端的设计工具，它允许用户在同一时间进行协作和设计。它的主要目的是提供一种简单易用的方式，让设计师和团队成员能够共同协作进行设计。

<!-- truncate -->

以下是Figma的一些基本概念：

1. 画布（Canvas）：画布是Figma中的主要设计区域，类似于Photoshop或Sketch中的画布。在画布上，用户可以创建不同的元素和布局。

2. 组件（Component）：组件是Figma中的一种重要元素，它是一组可重用的设计元素，可以在设计中多次使用。组件通常包括文本、形状、图像等元素，并可以在不同的画布中进行共享和更新。

3. 图层（Layer）：图层是Figma中的基本元素，它包含了设计中的文本、形状、图像等元素。用户可以通过对不同的图层进行调整和组合来创建复杂的设计。

4. 样式（Style）：样式是Figma中的一种基本元素，它定义了设计中的色彩、字体、边框、阴影等属性。样式可以应用于不同的元素，并可以随时修改和更新。

5. 插件（Plugin）：Figma还提供了许多插件，可以扩展其功能。这些插件可以用于自动化任务、增加设计资源等。

综上所述，Figma是一款基于云端的设计工具，支持团队协作和设计，其中包括画布、组件、图层、样式和插件等基本概念。

## 功能亮点：
Figma是一款功能强大的云端设计工具，具有许多功能亮点。以下是其中一些：

1. 协作功能：Figma是一款支持多人协作的设计工具，可以让设计师和团队成员在同一时间对同一个设计文件进行协作和编辑，可以通过评论、共享和版本控制等功能实现更好的协作。

2. 自动布局功能：Figma可以通过自动布局功能，使得设计师可以更快速地创建和修改布局，以及在不同设备和尺寸上进行预览和调整。

3. 组件和样式管理：Figma的组件和样式管理功能非常强大，可以让设计师创建可重用的组件和样式，以便在不同的设计中进行共享和更新。这些功能可以显著提高设计的效率和一致性。

4. 原型和动效功能：Figma具有丰富的原型和动效功能，可以让设计师创建交互式原型和动效。这些功能可以帮助设计师更好地展示和演示设计，并提供更好的用户体验。

5. 插件生态系统：Figma的插件生态系统非常丰富，可以帮助设计师扩展Figma的功能，例如自动生成设计资源、自动化任务、导入和导出文件等。

综上所述，Figma具有许多功能亮点，包括协作功能、自动布局、组件和样式管理、原型和动效功能以及插件生态系统。


## 必备插件：
1. Figma 到 HTML、CSS、React 等！
这是我最喜欢的插件之一。只需点击几下，您就可以将任何网站转换为 Figma 设计。想象一下将任何网站转换为设计并使用该网站组件。
下载插件的链接：[🔗](https://www.figma.com/community/plugin/747985167520967365/Figma-to-HTML%2C-CSS%2C-React-%26-more!)

![](https://img.wenhaofree.com/blog/1*uUJ3dmBytJMpy-WZ6wS_nw.png)


2. LottieFiles
使用 LottieFiles 插件，您可以将动画插入到您的设计中。您可以导入完全免费的 SVG 或 GIF 动画，LottiFiles 为您的项目提供了 1000 个动画。
下载插件的链接：[🔗](https://www.figma.com/community/plugin/809860933081065308/LottieFiles)

<img src="https://img.wenhaofree.com/blog/1*o2LyHr50G_z7lU1rt-AOmg.png"/>

3. 删除背景
如果您正在使用任何图像并且想要删除图像的背景，那么“删除 BG”是只需单击即可删除背景的最佳插件。
下载插件的链接：[🔗](https://www.figma.com/community/plugin/738992712906748191/Remove-BG)

<img src="https://img.wenhaofree.com/blog/1*-hF7H1uo_oKavGNnP8disA.png"/>

4. 自动化流程
Autoflow 是创建快速用户流程的最佳且简单的工具。您只需创建形状并连接它们即可在很短的时间内创建用户流程。
下载插件的链接：[🔗](https://www.figma.com/community/plugin/733902567457592893/Autoflow)

<img src="https://img.wenhaofree.com/blog/1*BRwjB2oDWCB7ELVyd2OAaA.gif"/>

5. Content Reel
Content Reel 是一个你可以获得一切的地方，几乎是一切；从图像到文本字符串，到图标，还有头像。我经常使用这个插件，这个插件是由微软创建的。这是我最重要、最省时间的插件。
下载插件的链接：[🔗](https://www.figma.com/community/plugin/731627216655469013/Content-Reel)

<img src="https://img.wenhaofree.com/blog/1*rZEZYM3_trFv9Mq8DZpTJg.png"/>


## 参考网址
- [Figma系列文章](https://www.figma.cool/learning-paths)
- [Figma必备20个插件](https://uxplanet.org/20-figma-plugins-every-designer-must-have-9bd6f1733780)
- [中文客户端下载地址](https://www.figma.com/downloads/)
- [官方中文教程](https://www.figma.cool/official-docs)
