---
slug: Installation-and-use-of-Xpath-Helper
title: Xpath Helper的安装和使用
date: 2023-11-13
authors: wenhao
tags: ['General']
keywords: ['Default']
---
https://c.biancheng.net/python_spider/xpath-helper.html 

为了帮助大家快速掌握 Xpath 表达式的使用，这里给大家推荐一款 Xpath 表达式匹配助软件，它就是 Xpath Helper。 
## Xpath Helper介绍

<!-- truncate -->

Xpath Helper 是一款谷歌应用商店推出的免费工具，因此您需要在谷歌商店进行下载。下载完毕后，谷歌浏览器会将其作为插件自动安装在扩展程序中，如下所示： 
![Image](https://prod-files-secure.s3.us-west-2.amazonaws.com/b0012720-ccd1-41ef-9ca9-02f55a45f30f/b75cb66b-22c0-4f97-b575-3b010d17475e/9-210Q9140614140.png?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Content-Sha256=UNSIGNED-PAYLOAD&X-Amz-Credential=AKIAT73L2G45HZZMZUHI%2F20231125%2Fus-west-2%2Fs3%2Faws4_request&X-Amz-Date=20231125T072020Z&X-Amz-Expires=3600&X-Amz-Signature=5d8ed8d90f87c68e95fcbd319e507bf54ec05215c445e843e0a4a5390af7c4d4&X-Amz-SignedHeaders=host&x-id=GetObject)
图 1：Xpath Helper助手 
点击扩展程序入口，进入管理扩展程序界面，如下图所示： 
![Image](https://prod-files-secure.s3.us-west-2.amazonaws.com/b0012720-ccd1-41ef-9ca9-02f55a45f30f/7287c29a-e185-480e-a345-8a9d70e14782/9-210Q9140623R4.gif?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Content-Sha256=UNSIGNED-PAYLOAD&X-Amz-Credential=AKIAT73L2G45HZZMZUHI%2F20231125%2Fus-west-2%2Fs3%2Faws4_request&X-Amz-Date=20231125T072020Z&X-Amz-Expires=3600&X-Amz-Signature=9b0c816feb24aa017dbbf702f52e6f8c1f813de708c9cf3bb01ae459268f4d95&X-Amz-SignedHeaders=host&x-id=GetObject)
图 2：Chrome浏览器插件管理界面 
您也可以通过以下步骤进入上述管理界面：浏览器设置 -> 更多工具 ->扩展程序 ->开发者模式。注意：此时右上角的开发者模式应处于开启状态。 
## Xpath Helper使用

<!-- truncate -->

安装完毕后，在需要匹配数据的页面处，使用快捷键打开助手工具（快捷键：ctrl+shift+x）,使用示意图如下： 
![Image](https://prod-files-secure.s3.us-west-2.amazonaws.com/b0012720-ccd1-41ef-9ca9-02f55a45f30f/aa3d8507-1f9d-4843-b044-dd2f6ab633b4/9-210Q9140632955.gif?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Content-Sha256=UNSIGNED-PAYLOAD&X-Amz-Credential=AKIAT73L2G45HZZMZUHI%2F20231125%2Fus-west-2%2Fs3%2Faws4_request&X-Amz-Date=20231125T072020Z&X-Amz-Expires=3600&X-Amz-Signature=bc9c60c03a8f8a97a3217a9be9674baa37dc6b643b5e72e0ef979e73f20505f1&X-Amz-SignedHeaders=host&x-id=GetObject)
图 3：Xpath使用示意图（ 点击看高清图 ） 
将鼠标悬停在需要选取数据的文本上，并按下 shift 按键就会自动出现 Xpath 表达式，然后再根据您自己的需求对表达式稍微修改即可。 
如果您没有谷歌应用商店账号，您也可以在网上搜索免费的下载资源。为了节省您的时间，下面提供了资源下载链接： 
```plain text
云盘链接：https://pan.baidu.com/s/18LcxOCLqALlob33UybTATA
提取码：eo1m
```
下载解压后，将文件夹直接拖入 Chrome 扩展程序即可完成安装。 
## 浏览器Xpath匹配助手

<!-- truncate -->

谷歌开发者调试工具也内置了 Xpath 表达式匹配功能，首先打开调试工具，在下方的调试工作区内使用快捷键 ctrl+F 打开 Xpath 匹配功能，如下图所示： 
![Image](https://prod-files-secure.s3.us-west-2.amazonaws.com/b0012720-ccd1-41ef-9ca9-02f55a45f30f/48b46613-f217-42ae-8060-961919f0f2dc/9-210Q9140642403.png?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Content-Sha256=UNSIGNED-PAYLOAD&X-Amz-Credential=AKIAT73L2G45HZZMZUHI%2F20231125%2Fus-west-2%2Fs3%2Faws4_request&X-Amz-Date=20231125T072020Z&X-Amz-Expires=3600&X-Amz-Signature=2087a0fc026bdffcb8e43f514ed13eeacb2d93af2eef3cd1836f513af9167a8d&X-Amz-SignedHeaders=host&x-id=GetObject)
图 4：Chrome调试工具Xpath使用（ 点击看高清图 ） 
关注公众号「站长严长生」，在手机上阅读所有教程，随时随地都能学习。内含一款搜索神器，免费下载全网书籍和视频。 



 > 在遵循创作的康庄大道上，若我的文字不慎踏入了他人的花园，请告之我，我将以最快的速度，携带着诚意和尊重，将它们从您的视野中撤去。
