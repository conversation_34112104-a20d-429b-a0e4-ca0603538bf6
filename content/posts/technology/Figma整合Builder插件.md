---
slug: Figma-builder
title: Figma整合Builder插件使用
date: 2022-03-16
authors: wenhao
tags: [U<PERSON>, Figma,Builder]
keywords: [<PERSON><PERSON>,<PERSON><PERSON>,<PERSON>uild<PERSON>,<PERSON><PERSON><PERSON>,文浩,<PERSON>]
description: 关于Figma整合Builder插件使用
---

## 最终效果：
> 使用Figma和Builder把设计的页面直接转换成React代码

<img src="https://img.wenhaofree.com/blog/image-20230724175709446.png"/>

<!-- truncate -->

## 教程:
一、Figma配置Builder：

1. Figma安装插件Builder：
[链接](https://www.figma.com/community/plugin/747985167520967365/Builder.io---Generate-designs-with-AI-%26-export-designs-to-code)

2. Chrome安装插件Html to Figma:
[链接](https://chrome.google.com/webstore/detail/html-to-figma-by-builderi/efjcmgblfpkhbjpkpopkgeomfkokpaim/related)

3. Figma点击插件使用导出code
4. 方式一：直接生成code
5. 方式二：使用下载json文件生成代码

<img src="https://img.wenhaofree.com/blog/Figma_builder.png"/>

二、Builder获取React代码
1. 选择方式二下载的json上传到Builder中
<img src="https://img.wenhaofree.com/blog/image-20230724175912683.png"/>

2. 点击“Get Code” 获取编程语言代码
3. 代码效果如下图：

<img src="https://img.wenhaofree.com/blog/BuilderCode.png"/>


## 参考文档：

- [配置插件Builder](https://www.builder.io/c/docs/import-from-figma)
- [Bilder地址](https://builder.io/fiddle/feb7fced705c483a862c42fd4952bc93?utm_source=figma&utm_content=import&figma_modal=true)
- [Figma地址](https://www.figma.com/file/w2DVyRaQ6gpkJW1ms0e7co/Builder.io---Generate-designs-with-AI-%26-export-designs-to-code-(Community)?type=design&node-id=4-2&mode=design&t=PcTbH9OD9b8jRhlN-0)