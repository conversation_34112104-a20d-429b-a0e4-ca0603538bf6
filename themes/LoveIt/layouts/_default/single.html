{{- define "title" }}{{ .Title }} - {{ .Site.Title }}{{ end -}}

{{- define "content" -}}
    {{- $params := .Scratch.Get "params" -}}
    <div class="page single special">
        {{- /* Title */ -}}
        <h1 class="single-title animate__animated animate__pulse animate__faster">
            {{- .Title -}}
        </h1>

        {{- /* Subtitle */ -}}
        {{- with $params.subtitle -}}
            <h2 class="single-subtitle">{{ . }}</h2>
        {{- end -}}

        {{- /* Top Ad */ -}}
        {{- $analytics := .Site.Params.analytics | default dict -}}
        {{- $adsense := $analytics.adsense | default dict -}}
        {{- if and $adsense.enable $adsense.topAd -}}
            <div class="adsense-top">
                {{- partial "plugin/adsense.html" (dict "Scratch" .Scratch "adsense" $adsense "position" "top") -}}
            </div>
        {{- end -}}

        {{- /* Content */ -}}
        <div class="content" id="content">
            {{- dict "Content" .Content "Ruby" $params.ruby "Fraction" $params.fraction "Fontawesome" $params.fontawesome | partial "function/content.html" | safeHTML -}}
        </div>

        {{- /* Bottom Ad */ -}}
        {{- if and $adsense.enable $adsense.bottomAd -}}
            <div class="adsense-bottom">
                {{- partial "plugin/adsense.html" (dict "Scratch" .Scratch "adsense" $adsense "position" "bottom") -}}
            </div>
        {{- end -}}

        {{- /* Comment */ -}}
        {{- partial "comment.html" . -}}
    </div>
{{- end -}}
