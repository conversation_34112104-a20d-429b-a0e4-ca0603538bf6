{{- .Scratch.Set "version" "0.3.1-DEV" -}}

{{- $params := .Params | merge .Site.Params.page -}}

{{- if eq hugo.Environment "production" -}}
    {{- $cdn := .Site.Params.cdn -}}
    {{- with $cdn.data -}}
        {{- $cdnData := printf "data/cdn/%v" . | resources.Get | transform.Unmarshal -}}
        {{- $cdn = dict "simpleIconsPrefix" $cdnData.prefix.simpleIcons -}}
        {{- $prefix := $cdnData.prefix.libFiles | default "" -}}
        {{- range $key, $value := $cdnData.libFiles -}}
            {{- $cdn = printf "%v%v" $prefix $value | dict $key | merge $cdn -}}
        {{- end -}}
    {{- end -}}
    {{- .Scratch.Set "cdn" $cdn -}}
    {{- .Scratch.Set "fingerprint" .Site.Params.fingerprint -}}
    {{- .Scratch.Set "analytics" .Site.Params.analytics -}}
    {{- .Scratch.Set "comment" $params.comment -}}
    {{- if eq .Params.comment true -}}
        {{- .Scratch.Set "comment" .Site.Params.comment -}}
    {{- else if eq .Params.comment false -}}
        {{- .Scratch.Set "comment" dict -}}
    {{- end -}}
{{- else if eq .Site .Sites.Default -}}
    {{- .Scratch.Set "analytics" .Site.Params.analytics -}}
    {{- warnf "Current environment is not \"production\". The \"comment system\", \"CDN\" and \"fingerprint\" will be disabled.\n" -}}
    {{- warnf "当前运行环境不是 \"production\". \"评论系统\", \"CDN\" 和 \"fingerprint\" 不会启用.\n" -}}
{{- end -}}

{{- .Scratch.Set "params" $params -}}
{{- .Scratch.Set "this" dict -}}

{{- partial "plugin/compatibility.html" . -}}
