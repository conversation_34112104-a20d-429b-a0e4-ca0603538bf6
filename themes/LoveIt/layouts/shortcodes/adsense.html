{{- $analytics := .Site.Params.analytics | default dict -}}
{{- $adsense := $analytics.adsense | default dict -}}

{{- if $adsense.enable -}}
    {{- $slot := .Get "slot" | default $adsense.defaultSlot -}}
    {{- $format := .Get "format" | default "auto" -}}
    {{- $style := .Get "style" | default "display:block" -}}
    {{- $responsive := .Get "responsive" | default "true" -}}
    {{- $margin := .Get "margin" | default "20px 0" -}}
    
    {{- if $slot -}}
        <div class="adsense-shortcode" style="text-align: center; margin: {{ $margin }};">
            <ins class="adsbygoogle"
                 style="{{ $style }}"
                 data-ad-client="{{ $adsense.client }}"
                 data-ad-slot="{{ $slot }}"
                 {{- if ne $format "auto" }}data-ad-format="{{ $format }}"{{ end }}
                 {{- if eq $responsive "true" }}data-full-width-responsive="true"{{ end }}></ins>
            <script>
                (adsbygoogle = window.adsbygoogle || []).push({});
            </script>
        </div>
    {{- else -}}
        {{- warnf "AdSense shortcode requires a slot parameter or defaultSlot in site configuration" -}}
    {{- end -}}
{{- else -}}
    {{- if hugo.IsServer -}}
        <div style="background: #f0f0f0; padding: 20px; text-align: center; margin: 20px 0; border: 1px dashed #ccc;">
            <p style="margin: 0; color: #666;">AdSense Ad Placeholder (slot: {{ .Get "slot" | default "not specified" }})</p>
        </div>
    {{- end -}}
{{- end -}}
