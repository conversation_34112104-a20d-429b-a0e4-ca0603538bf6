# Umami Analytics 集成指南

## 📊 什么是 Umami

Umami 是一个简单、快速、隐私友好的 Google Analytics 替代方案。它提供了您需要的所有网站分析功能，同时完全尊重用户隐私。

### ✨ 主要特性

- 🚀 **轻量级**: 脚本大小仅约 2KB
- 🔒 **隐私友好**: 不使用 cookies，符合 GDPR
- 🏠 **自托管**: 完全控制您的数据
- ⚡ **实时统计**: 实时查看访问者数据
- 🌐 **多域名支持**: 支持跟踪多个域名
- 📱 **响应式**: 完美支持移动设备
- 🔓 **开源**: 完全开源，可自定义

## 🚀 快速开始

### 1. 基础配置

在您的 `hugo.yaml` 文件中添加以下配置：

```yaml
params:
  analytics:
    umami:
      enable: true
      server: "https://umami.wenhaofree.com"
      websiteId: "871f4046-f21c-4e43-aae2-483143b6fb04"
      respectDoNotTrack: false
```

### 2. 立即生效

保存配置文件后，Umami 跟踪代码将自动加载到您的网站中。

## ⚙️ 详细配置

### 完整配置选项

```yaml
params:
  analytics:
    umami:
      enable: true                    # 启用/禁用 Umami
      server: "https://umami.wenhaofree.com"  # Umami 服务器地址
      websiteId: "871f4046-f21c-4e43-aae2-483143b6fb04"  # 网站 ID
      respectDoNotTrack: false        # 是否遵循 DNT 设置
      domains: ""                     # 多域名跟踪（可选）
      autoTrack: "true"              # 自动跟踪页面浏览
      doNotTrack: "false"            # 启用请勿跟踪
      cache: "false"                 # 启用脚本缓存
```

### 配置参数说明

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `enable` | boolean | `false` | 启用/禁用 Umami 跟踪 |
| `server` | string | - | Umami 服务器地址 |
| `websiteId` | string | - | 从 Umami 仪表板获取的网站 ID |
| `respectDoNotTrack` | boolean | `false` | 是否遵循浏览器的 DNT 设置 |
| `domains` | string | - | 多域名跟踪，逗号分隔 |
| `autoTrack` | string | `"true"` | 是否自动跟踪页面浏览 |
| `doNotTrack` | string | `"false"` | 是否启用请勿跟踪 |
| `cache` | string | `"false"` | 是否启用脚本缓存 |

## 🔧 高级配置

### 多域名跟踪

如果您需要在多个域名上跟踪同一个网站：

```yaml
params:
  analytics:
    umami:
      enable: true
      server: "https://umami.wenhaofree.com"
      websiteId: "871f4046-f21c-4e43-aae2-483143b6fb04"
      domains: "example.com,blog.example.com,shop.example.com"
```

### 隐私增强配置

为了最大程度保护用户隐私：

```yaml
params:
  analytics:
    umami:
      enable: true
      server: "https://umami.wenhaofree.com"
      websiteId: "871f4046-f21c-4e43-aae2-483143b6fb04"
      respectDoNotTrack: true
      doNotTrack: "true"
```

### 性能优化配置

启用缓存以提高加载性能：

```yaml
params:
  analytics:
    umami:
      enable: true
      server: "https://umami.wenhaofree.com"
      websiteId: "871f4046-f21c-4e43-aae2-483143b6fb04"
      cache: "true"
```

## 📈 与其他分析工具共存

Umami 可以与其他分析工具同时使用：

```yaml
params:
  analytics:
    # Google Analytics 4
    google:
      id: "G-XXXXXXXXXX"
      respectDoNotTrack: false
    
    # Umami Analytics
    umami:
      enable: true
      server: "https://umami.wenhaofree.com"
      websiteId: "871f4046-f21c-4e43-aae2-483143b6fb04"
      respectDoNotTrack: false
    
    # Google AdSense
    adsense:
      enable: true
      client: "ca-pub-1234567890123456"
```

## 🛠️ 故障排除

### 常见问题

1. **跟踪代码未加载**
   - 检查 `enable: true` 是否设置
   - 确认 `websiteId` 是否正确
   - 验证 `server` 地址是否可访问

2. **数据未显示**
   - 确认网站 ID 在 Umami 仪表板中存在
   - 检查浏览器控制台是否有错误
   - 验证 Umami 服务器状态

3. **多域名跟踪问题**
   - 确认所有域名都在 `domains` 参数中
   - 检查域名格式是否正确（不包含协议）

### 调试模式

在开发环境中查看跟踪代码：

```bash
hugo server --environment development
```

然后在浏览器开发者工具中查看网络请求，确认 Umami 脚本是否正确加载。

## 📊 数据查看

访问您的 Umami 仪表板查看分析数据：
- 实时访问者
- 页面浏览量
- 访问来源
- 设备信息
- 地理位置

## 🔗 相关链接

- [Umami 官方网站](https://umami.is/)
- [Umami GitHub](https://github.com/umami-software/umami)
- [Umami 文档](https://umami.is/docs)

## 📝 注意事项

1. **隐私合规**: Umami 不使用 cookies，天然符合 GDPR
2. **性能影响**: 脚本非常轻量，对网站性能影响极小
3. **数据所有权**: 使用自托管 Umami，您完全拥有数据
4. **实时性**: 与 Google Analytics 不同，Umami 提供真正的实时数据

## 🆕 更新日志

- ✅ 添加 Umami Analytics 完整支持
- ✅ 支持自托管 Umami 实例
- ✅ 隐私友好的跟踪实现
- ✅ 与 Google Analytics 和 AdSense 共存
- ✅ 生产和开发环境支持
- ✅ 简化的配置和部署
