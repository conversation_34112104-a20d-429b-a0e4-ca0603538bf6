# Google AdSense & Umami Analytics 集成指南

## 📋 概述

LoveIt 主题现已完全支持 Google AdSense 广告集成和 Umami Analytics 跟踪，包括：

### Google AdSense 功能

- ✅ 自动广告 (Auto Ads)
- ✅ 展示广告 (Display Ads)
- ✅ 信息流广告 (In-feed Ads)
- ✅ 文章内广告 (In-article Ads)
- ✅ 多重广告 (Multiplex Ads)
- ✅ Shortcode 支持
- ✅ 开发环境占位符

### Umami Analytics 功能

- ✅ 轻量级网站分析
- ✅ 隐私友好，符合 GDPR
- ✅ 自托管支持
- ✅ 实时访问统计
- ✅ Do Not Track 支持
- ✅ 多域名跟踪

## 🚀 快速开始

### Umami Analytics 配置

在您的 `hugo.yaml` 文件中添加：

```yaml
params:
  analytics:
    umami:
      enable: true
      server: "https://umami.wenhaofree.com"
      websiteId: "871f4046-f21c-4e43-aae2-483143b6fb04"
      respectDoNotTrack: false
```

### Google AdSense 基础配置

在您的 `hugo.yaml` 文件中添加：

```yaml
params:
  analytics:
    adsense:
      enable: true
      client: "ca-pub-您的客户端ID"
      defaultSlot: "您的默认广告位ID"
      autoAds: true
```

### 2. 在文章中使用广告

使用 shortcode 在文章中插入广告：

```markdown
<!-- 基础用法 -->
{{< adsense slot="1234567890" >}}

<!-- 自定义格式 -->
{{< adsense slot="1234567890" format="rectangle" responsive="false" >}}

<!-- 自定义样式 -->
{{< adsense slot="1234567890" style="width:300px;height:250px" margin="30px 0" >}}
```

## ⚙️ 详细配置

### 完整配置示例

```yaml
params:
  analytics:
    adsense:
      enable: true
      client: "ca-pub-1234567890123456"
      defaultSlot: "1234567890"
      autoAds: true
      topAd: true      # 文章顶部广告
      bottomAd: true   # 文章底部广告
      
      # 展示广告
      displayAds:
        - slot: "1234567890"
          display: "block"
          format: "auto"
          responsive: true
          margin: "20px 0"
        
      # 信息流广告
      infeedAds:
        - slot: "1111111111"
          layoutKey: "-6t+ed+2i-1n-4w"
          
      # 文章内广告
      inarticleAds:
        - slot: "2222222222"
          
      # 多重广告
      multiplexAds:
        - slot: "3333333333"
```

### Shortcode 参数

| 参数 | 说明 | 默认值 | 示例 |
|------|------|--------|------|
| `slot` | 广告位 ID | 必需 | `"1234567890"` |
| `format` | 广告格式 | `"auto"` | `"rectangle"`, `"banner"` |
| `responsive` | 响应式 | `"true"` | `"false"` |
| `style` | 自定义样式 | `"display:block"` | `"width:300px;height:250px"` |
| `margin` | 外边距 | `"20px 0"` | `"30px auto"` |

## 📍 广告位置

### 自动位置

- **文章顶部**: 设置 `topAd: true`
- **文章底部**: 设置 `bottomAd: true`
- **自动广告**: 设置 `autoAds: true`

### 手动位置

使用 shortcode 在文章中的任意位置插入广告：

```markdown
# 文章标题

这是文章开头...

{{< adsense slot="1234567890" >}}

这是文章中间部分...

{{< adsense slot="0987654321" format="rectangle" >}}

这是文章结尾...
```

## 🛠️ 开发环境

在开发环境中（`hugo server`），广告会显示为占位符：

```
┌─────────────────────────────────┐
│ AdSense Ad Placeholder          │
│ (slot: 1234567890)             │
└─────────────────────────────────┘
```

## 📝 注意事项

1. **客户端 ID**: 格式为 `ca-pub-xxxxxxxxxx`
2. **广告位 ID**: 在 AdSense 后台创建广告位时获得
3. **政策合规**: 确保网站内容符合 Google AdSense 政策
4. **加载性能**: 自动广告可能影响页面加载速度
5. **移动优化**: 建议启用响应式广告

## 🔧 故障排除

### 广告不显示

1. 检查客户端 ID 和广告位 ID 是否正确
2. 确认 AdSense 账户状态正常
3. 检查网站是否被 AdSense 批准
4. 验证广告代码是否正确加载

### 开发环境问题

- 开发环境中广告显示为占位符是正常的
- 只有在生产环境中才会显示真实广告

## 📚 相关文件

- `themes/LoveIt/layouts/partials/plugin/analytics.html` - 分析代码集成
- `themes/LoveIt/layouts/partials/plugin/adsense.html` - AdSense 组件
- `themes/LoveIt/layouts/shortcodes/adsense.html` - AdSense shortcode
- `themes/LoveIt/layouts/_default/single.html` - 单页面布局（包含广告位）

## 🆕 更新日志

- ✅ 添加 Google AdSense 完整支持
- ✅ 支持多种广告格式
- ✅ 添加 shortcode 功能
- ✅ 集成到文章页面布局
- ✅ 开发环境占位符支持
