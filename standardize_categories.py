#!/usr/bin/env python3
"""
Script to standardize category metadata in Hugo markdown files.
Converts from 'category: "value"' to 'categories: ["value"]' format
and ensures consistent bilingual category mapping.
"""

import os
import re
import glob

# Category mapping between Chinese and English
CATEGORY_MAPPING = {
    "技术评测": "Tech Review",
    "技术教程": "Tech Tutorial", 
    "科技资讯": "Tech News",
    "开发工具": "Development Tools",
    "AI技术": "AI Technology",
    "技术": "Technology"
}

def process_file(filepath):
    """Process a single markdown file to standardize categories."""
    print(f"Processing: {filepath}")
    
    with open(filepath, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Check if this is an English file
    is_english = filepath.endswith('.en.md')
    
    # Find and replace category: with categories:
    # Pattern to match: category: "value"
    category_pattern = r'^category:\s*"([^"]+)"'
    
    def replace_category(match):
        category_value = match.group(1)
        
        # If it's an English file, ensure we use English categories
        if is_english and category_value in CATEGORY_MAPPING.values():
            # Already in English, just convert format
            return f'categories: ["{category_value}"]'
        elif is_english:
            # Try to find English equivalent
            for cn, en in CATEGORY_MAPPING.items():
                if category_value == cn:
                    return f'categories: ["{en}"]'
            # If no mapping found, keep as is
            return f'categories: ["{category_value}"]'
        else:
            # Chinese file, keep Chinese category
            return f'categories: ["{category_value}"]'
    
    # Apply the replacement
    new_content = re.sub(category_pattern, replace_category, content, flags=re.MULTILINE)
    
    # Check if any changes were made
    if new_content != content:
        with open(filepath, 'w', encoding='utf-8') as f:
            f.write(new_content)
        print(f"  ✓ Updated categories format")
        return True
    else:
        print(f"  - No changes needed")
        return False

def main():
    """Main function to process all markdown files in technology directory."""
    technology_dir = "content/posts/technology"
    
    if not os.path.exists(technology_dir):
        print(f"Error: Directory {technology_dir} not found")
        return
    
    # Find all markdown files
    md_files = glob.glob(os.path.join(technology_dir, "*.md"))
    
    print(f"Found {len(md_files)} markdown files to process")
    print("=" * 50)
    
    updated_count = 0
    
    for filepath in sorted(md_files):
        if process_file(filepath):
            updated_count += 1
    
    print("=" * 50)
    print(f"Processing complete. Updated {updated_count} files.")

if __name__ == "__main__":
    main()
