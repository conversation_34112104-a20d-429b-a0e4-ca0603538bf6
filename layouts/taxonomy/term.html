{{- define "title" -}}
    {{- .Title }} - {{ T .Data.Singular | default .Data.Singular }} - {{ .Site.Title -}}
{{- end -}}

{{- define "content" -}}
    <div class="page archive">
        {{- /* Title */ -}}
        <h2 class="single-title animate__animated animate__pulse animate__faster">
            {{- $taxonomy := .Data.Singular -}}
            {{- if eq $taxonomy "category" -}}
                <i class="far fa-folder-open fa-fw" aria-hidden="true"></i>&nbsp;{{ .Title }}
            {{- else if eq $taxonomy "tag" -}}
                <i class="fas fa-tag fa-fw" aria-hidden="true"></i>&nbsp;{{ .Title }}
            {{- else -}}
                {{- printf "%v - %v" (T $taxonomy | default $taxonomy) .Title -}}
            {{- end -}}
        </h2>

        {{- /* Paginate */ -}}
        {{- if .Pages -}}
            {{- $pages := .Pages.GroupByDate "2006" -}}
            {{- with .Site.Params.list.paginate | default .Site.Params.paginate -}}
                {{- $pages = $.Paginate $pages . -}}
            {{- else -}}
                {{- $pages = .Paginate $pages -}}
            {{- end -}}
            {{- range $pages.PageGroups -}}
                <h3 class="group-title">{{ .Key }}</h3>
                {{- range .Pages -}}
                    <article class="archive-item">
                        <a href="{{ .RelPermalink }}" class="archive-item-link">
                            {{- .Title | emojify -}}
                        </a>
                        <span class="archive-item-date">
                            {{- $.Site.Params.list.dateFormat | default "01-02" | .Date.Format -}}
                        </span>
                    </article>
                {{- end -}}
            {{- end -}}
            {{- partial "paginator.html" . -}}
        {{- end -}}
    </div>
{{- end -}}
