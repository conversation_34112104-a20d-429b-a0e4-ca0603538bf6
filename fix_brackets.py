#!/usr/bin/env python3
"""
Script to fix extra brackets in markdown files.
"""

import os
import re
import glob

def fix_file(filepath):
    """Fix extra brackets in a single markdown file."""
    print(f"Fixing: {filepath}")
    
    with open(filepath, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Fix double closing brackets
    content = re.sub(r'\]\]', ']', content)
    
    with open(filepath, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print(f"  ✓ Fixed brackets")

def main():
    """Main function to fix all markdown files in technology directory."""
    technology_dir = "content/posts/technology"
    
    # Find all markdown files
    md_files = glob.glob(os.path.join(technology_dir, "*.md"))
    
    for filepath in sorted(md_files):
        fix_file(filepath)
    
    print("Bracket fixing complete.")

if __name__ == "__main__":
    main()
