# Google AdSense 配置示例
# 将以下配置添加到您的 hugo.yaml 文件中

params:
  analytics:
    # Google AdSense 配置
    adsense:
      enable: true
      # 您的 AdSense 客户端 ID (格式: ca-pub-xxxxxxxxxx)
      client: "ca-pub-1234567890123456"
      # shortcode 默认广告位 ID
      defaultSlot: "1234567890"
      # 启用自动广告
      autoAds: true
      # 文章顶部广告
      topAd: true
      # 文章底部广告  
      bottomAd: true
      
      # 展示广告配置 (可选)
      displayAds:
        - slot: "1234567890"
          display: "block"
          format: "auto"
          responsive: true
          margin: "20px 0"
          style: "width: 100%; height: 280px;"
        - slot: "0987654321"
          display: "inline-block"
          format: "rectangle"
          responsive: false
          margin: "15px 0"
          style: "width: 300px; height: 250px;"
      
      # 信息流广告配置 (可选)
      infeedAds:
        - slot: "1111111111"
          layoutKey: "-6t+ed+2i-1n-4w"
          margin: "30px 0"
      
      # 文章内广告配置 (可选)
      inarticleAds:
        - slot: "2222222222"
          margin: "40px 0"
      
      # 多重广告配置 (可选)
      multiplexAds:
        - slot: "3333333333"
          margin: "25px 0"

# 使用方法:
# 1. 在文章中使用 shortcode:
#    {{< adsense slot="1234567890" >}}
#    {{< adsense slot="1234567890" format="rectangle" responsive="false" >}}
#
# 2. 自动广告会在页面加载时自动显示
#
# 3. 文章顶部和底部广告通过配置控制显示

# 注意事项:
# - 请将 ca-pub-1234567890123456 替换为您的真实 AdSense 客户端 ID
# - 请将广告位 ID 替换为您在 AdSense 后台创建的真实广告位 ID
# - 在开发环境中，广告会显示为占位符
# - 确保您的网站符合 Google AdSense 政策要求
