#!/usr/bin/env python3
"""
Comprehensive script to analyze and fix category metadata in Hugo markdown files.
"""

import os
import re
import glob
import yaml

# Category mapping between Chinese and English
CATEGORY_MAPPING = {
    "技术评测": "Tech Review",
    "技术教程": "Tech Tutorial", 
    "科技资讯": "Tech News",
    "开发工具": "Development Tools",
    "AI技术": "AI Technology",
    "技术": "Technology"
}

def extract_front_matter(content):
    """Extract YAML front matter from markdown content."""
    if not content.startswith('---'):
        return None, content
    
    try:
        # Find the end of front matter
        end_marker = content.find('\n---\n', 3)
        if end_marker == -1:
            return None, content
        
        front_matter_str = content[3:end_marker]
        body = content[end_marker + 5:]
        
        # Parse YAML
        front_matter = yaml.safe_load(front_matter_str)
        return front_matter, body
    except:
        return None, content

def determine_category(filepath, front_matter):
    """Determine appropriate category based on file content and metadata."""
    filename = os.path.basename(filepath)
    is_english = filepath.endswith('.en.md')
    
    # Check existing tags for hints
    tags = front_matter.get('tags', []) if front_matter else []
    
    # Default categorization logic
    if any(tag in ['AI', 'AI模型', 'AI技术', '人工智能', 'Artificial Intelligence'] for tag in tags):
        return "AI Technology" if is_english else "AI技术"
    elif any(tag in ['开发工具', 'Development Tools', 'Tools'] for tag in tags):
        return "Development Tools" if is_english else "开发工具"
    elif any(tag in ['教程', 'Tutorial', 'Guide'] for tag in tags):
        return "Tech Tutorial" if is_english else "技术教程"
    elif any(tag in ['新闻', 'News'] for tag in tags):
        return "Tech News" if is_english else "科技资讯"
    else:
        return "Technology" if is_english else "技术"

def process_file(filepath):
    """Process a single markdown file."""
    print(f"Processing: {filepath}")
    
    with open(filepath, 'r', encoding='utf-8') as f:
        content = f.read()
    
    front_matter, body = extract_front_matter(content)
    
    if front_matter is None:
        print(f"  ⚠️  No valid front matter found")
        return False
    
    is_english = filepath.endswith('.en.md')
    changes_made = False
    
    # Check if categories exist
    if 'categories' not in front_matter:
        # Determine category
        category = determine_category(filepath, front_matter)
        front_matter['categories'] = [category]
        changes_made = True
        print(f"  ✓ Added category: {category}")
    else:
        # Validate existing categories
        categories = front_matter['categories']
        if isinstance(categories, str):
            # Convert string to list
            front_matter['categories'] = [categories]
            changes_made = True
            print(f"  ✓ Converted category to list format")
        
        # Check if English/Chinese categories match the language
        for i, cat in enumerate(front_matter['categories']):
            if is_english and cat in CATEGORY_MAPPING:
                # Chinese category in English file
                front_matter['categories'][i] = CATEGORY_MAPPING[cat]
                changes_made = True
                print(f"  ✓ Translated category: {cat} → {CATEGORY_MAPPING[cat]}")
            elif not is_english and cat in CATEGORY_MAPPING.values():
                # English category in Chinese file
                for cn, en in CATEGORY_MAPPING.items():
                    if cat == en:
                        front_matter['categories'][i] = cn
                        changes_made = True
                        print(f"  ✓ Translated category: {cat} → {cn}")
                        break
    
    # Write back if changes were made
    if changes_made:
        # Reconstruct the file
        new_content = "---\n" + yaml.dump(front_matter, default_flow_style=False, allow_unicode=True) + "---\n" + body
        
        with open(filepath, 'w', encoding='utf-8') as f:
            f.write(new_content)
        
        return True
    else:
        print(f"  - No changes needed")
        return False

def validate_bilingual_pairs():
    """Validate that bilingual pairs have consistent categories."""
    technology_dir = "content/posts/technology"
    
    # Find all Chinese files that have English counterparts
    chinese_files = glob.glob(os.path.join(technology_dir, "*.md"))
    chinese_files = [f for f in chinese_files if not f.endswith('.en.md')]
    
    print("\n" + "="*50)
    print("VALIDATING BILINGUAL CATEGORY CONSISTENCY")
    print("="*50)
    
    for chinese_file in chinese_files:
        english_file = chinese_file.replace('.md', '.en.md')
        
        if os.path.exists(english_file):
            # Check both files
            with open(chinese_file, 'r', encoding='utf-8') as f:
                cn_content = f.read()
            with open(english_file, 'r', encoding='utf-8') as f:
                en_content = f.read()
            
            cn_front_matter, _ = extract_front_matter(cn_content)
            en_front_matter, _ = extract_front_matter(en_content)
            
            if cn_front_matter and en_front_matter:
                cn_categories = cn_front_matter.get('categories', [])
                en_categories = en_front_matter.get('categories', [])
                
                # Check if categories are properly translated
                expected_en_categories = []
                for cn_cat in cn_categories:
                    if cn_cat in CATEGORY_MAPPING:
                        expected_en_categories.append(CATEGORY_MAPPING[cn_cat])
                    else:
                        expected_en_categories.append(cn_cat)  # Keep as is if no mapping
                
                if set(en_categories) != set(expected_en_categories):
                    print(f"❌ Mismatch: {os.path.basename(chinese_file)}")
                    print(f"   Chinese: {cn_categories}")
                    print(f"   English: {en_categories}")
                    print(f"   Expected: {expected_en_categories}")
                else:
                    print(f"✅ Match: {os.path.basename(chinese_file)}")

def main():
    """Main function."""
    technology_dir = "content/posts/technology"
    
    if not os.path.exists(technology_dir):
        print(f"Error: Directory {technology_dir} not found")
        return
    
    # Find all markdown files
    md_files = glob.glob(os.path.join(technology_dir, "*.md"))
    
    print(f"Found {len(md_files)} markdown files to process")
    print("=" * 50)
    
    updated_count = 0
    
    for filepath in sorted(md_files):
        if process_file(filepath):
            updated_count += 1
    
    print("=" * 50)
    print(f"Processing complete. Updated {updated_count} files.")
    
    # Validate bilingual pairs
    validate_bilingual_pairs()

if __name__ == "__main__":
    main()
